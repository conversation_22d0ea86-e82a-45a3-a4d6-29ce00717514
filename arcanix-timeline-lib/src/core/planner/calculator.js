
//Represent the core algortihims of this library
function planner(launchDate = Date.now(), tasks, fixedTasks = []) {
    const cursor = 0,
      visited = new Map()
    // launchDate = launchDate || Date.now();
  
    //TODO HANDLE SEQUENCIAL TASKS THAT STILL HAVE 
    tasks = sanitizeTasks(tasks);
    
    //Planned 
    if (fixedTasks.length >= 1) {
      fixedTasks = sanitizeTasksForFixed(fixedTasks);
      for (let i = 0; i < fixedTasks.length; i++) {
        const task = fixedTasks[i];
        const overlappedTask = canOverlapWithOtherTasks(task, visited)
        if (overlappedTask) {
          task.cursor = overlappedTask.cursor + 1
        } else if (!overlappedTask) {
          task.cursor = cursor
        }
        visited.set(task._id, task); // Mark task as visited
        continue;
      }
    }
  
  
    // Process tasks in the correct order
    for (let index = 0; index < tasks.length; index++) {
      const task = tasks[index];
  
      // If task is not in visited and task.dependencies is empty and visited is empty
      if (!visited.has(task._id) && (!task.deps || task.deps.length === 0) && visited.size === 0) {
        task.cursor = cursor;
        task.end = launchDate;
        task.start = addDays(launchDate, task.duration); // Backward schedule
  
        // task.row = 1;
        visited.set(task._id, task); // Mark task as visited
  
        // Adjust backward schedule if necessary
        // adjustedDateBackward(visited, task);
        continue;
      }
  
  
      // If the maximum dependency is in visited and it doesn't already have a sub-task
      const farthestDep = getFarthestDependency(task.deps, tasks);
      if (farthestDep && task.is_parallel && visited.get(farthestDep) && (visited.get(farthestDep).visitedSucessorTasks.size === 0)) {
        task.cursor = visited.get(farthestDep).cursor;
        task.end = addDays(visited.get(farthestDep).start, 1); // Start after the dependency ends
        task.start = addDays(task.end, task.duration);
        // task.row =  dynamicallyGetRow(task, visited);
        visited.get(farthestDep).visitedSucessorTasks.set(task._id, task); // Add to successor tasks
        visited.set(task._id, task);
  
        // Adjust backward schedule if necessary
        // adjustedDateBackward(visited, task);
        continue;
      }
  
      // If visited already has successor sub-task
      if (farthestDep && task.is_parallel && visited.get(farthestDep)?.visitedSucessorTasks.size > 0) {
        const lastSuccessor = Array.from(
          visited.get(farthestDep).visitedSucessorTasks.values()
        ).slice(-1)[0];
        task.cursor = lastSuccessor.cursor + 1; // Increment cursor based on last successor
  
        task.end = addDays(visited.get(farthestDep).start, 1);
        task.start = addDays(task.end, task.duration);
        // task.row = dynamicallyGetRow(task, visited);
        visited.get(farthestDep).visitedSucessorTasks.set(task._id, task);
        visited.set(task._id, task);
  
        // Adjust backward schedule if necessary
        // adjustedDateBackward(visited, task);
        continue;
      }
  
      // If task is_parallel is false and not dependent
      if (!task.is_parallel) {
        const lastVisited = Array.from(visited.values()).findLast((t) => t.cursor === 0);
        if (lastVisited) {
          task.cursor = 0; // Set the cursor as 0
          task.end = addDays(lastVisited.start, 1); // Start after the last visited task ends
          task.start = addDays(task.end, task.duration);
          // task.row = dynamicallyGetRow(task, visited);
          lastVisited.visitedSucessorTasks.set(task._id, task);
          visited.set(task._id, task);
  
          // Adjust backward schedule if necessary
          continue;
        }
      }
  
      // If task is_parallel and has no dependencies
      if (task.is_parallel && (!task.deps || task.deps.length === 0)) {
        const lastVisited = [...visited.entries()].at(-1)[1];
        if (!lastVisited.is_parallel) {
          task.cursor = cursor
          task.end = addDays(lastVisited.start, 1);
          task.start = addDays(task.end, task.duration);
          // visited.set(task._id, task);
        }
        if (lastVisited.is_parallel) {
          task.cursor = lastVisited.cursor + 1
          task.end = lastVisited.start;
          task.start = addDays(task.end, task.duration)
        }
        // task.cursor = Array.from(visited.values()).slice(-1)[0]?.row === 1 ? 0 : 1;
        // task.end = launchDate;
        // task.start = addDays(launchDate, task.duration); // Backward schedule
        // task.row = 1;
        lastVisited.visitedSucessorTasks.set(task._id, task);
        visited.set(task._id, task);
  
        continue;
      }
  
      if (task.is_parallel && task.deps.length > 0 && !visited.get(farthestDep)) {
        // Move the task to the end of the queue for reprocessing
        tasks.splice(index, 1); // Remove the task from its current position
        tasks.push(task); // Add it to the end of the array
        index--; // Decrement index to ensure the loop doesn't skip the next task
        continue;
      }
    }
  
    return tasks;
  }
  
  // Helper function to get the farthest dependency based on position in the list
  function getFarthestDependency(depList, tasks) {
    if (!depList || depList.length === 0) return null;
    const listIds = tasks.map(item => item._id);
    const sortedDeps = depList.sort((a, b) => listIds.indexOf(b) - listIds.indexOf(a));
    return sortedDeps[0];
  }
  
  // Dynamically get the row based on logic
  function dynamicallyGetRow(task, visited) {
    // Example logic for assigning rows
    return visited.size + 1; // Adjust based on your requirements
  }
  
  // Adjust the schedule for backward dates
  function adjustedDateBackward(visited, task) {
    // Adjust other tasks if necessary based on backward scheduling
    // This function should ensure no overlap or conflicts in scheduling
  }
  
  
  function sanitizeTasks(tasks) {
    // Handle null, undefined, or empty array cases
    if (!tasks || tasks.length === 0) {
      return [];
    }
  
    return tasks.map(task => ({
      _id: task._id,
      name_text: task.name_text,
      duration: task.duration,
      deps: (task.dependencies_list_custom_dependency || [])
        .filter(dep =>
          dep &&
          dep.type === 'successor' &&
          dep.is_dependent_into === false &&
          dep.data_type === 'deliverable'
        )
        .map(dep => dep.obj._id),
      is_parallel: task.is_parallel || false,
      priority_option: task.priority_option,
      start: undefined,
      end: undefined,
      cursor: undefined,
      row: undefined,
      col: undefined,
      visitedSucessorTasks: new Map()
    }));
  }
  
  function sanitizeTasksForFixed(tasks) {
    // Handle null, undefined, or empty array cases
    if (!tasks || tasks.length === 0) {
      return [];
    }
  
    return tasks.map(task => ({
      _id: task._id,
      name_text: task.name_text,
      duration: task.duration,
      deps: (task.dependencies_list_custom_dependency || [])
        .filter(dep =>
          dep &&
          dep.type === 'successor' &&
          dep.is_dependent_into === false &&
          dep.data_type === 'deliverable'
        )
        .map(dep => dep.obj._id),
      is_parallel: task.is_parallel || false,
      priority_option: task.priority_option,
      start: task.start,
      end: task.end,
      cursor: undefined,
      row: undefined,
      col: undefined,
      visitedSucessorTasks: new Map()
    }));
  }
  
  function canOverlapWithOtherTasks(task, visited) {
    let lastOverlappingTask = null;
  
    // Iterate through the visited tasks
    for (const visitedTask of visited.values()) {
      // Check if the tasks overlap in their date ranges
      if (
        (task.start >= visitedTask.start && task.start <= visitedTask.end) || // Task starts during the visited task
        (task.end >= visitedTask.start && task.end <= visitedTask.end) || // Task ends during the visited task
        (visitedTask.start >= task.start && visitedTask.start <= task.end) // Visited task starts during the current task
      ) {
        lastOverlappingTask = visitedTask; // Update the last overlapping task
      }
    }
  
    // Return the last overlapping task or null if none found
    return lastOverlappingTask;
  }
  
  function addDays(date, days) { const newDate = new Date(date); newDate.setDate(date.getDate() - days); return newDate; }
  
  module.exports = planner;
