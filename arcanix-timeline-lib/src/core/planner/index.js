const { sortTasksByDependency } = require('./sort');
const planner = require('./calculator');
class ReverseSchedulingAlgorithm {
  /**
   * Constructor to initialize the scheduling algorithm with configuration.
   * @param {Object} options - Configuration options for the algorithm.
   * @property {string} currentDate - The current date.
   * @property {string} today - The date representing "today".
   * @property {Array} blockedDates - List of dates that are blocked.
   * @property {Object} config - Configuration options.
   */
  constructor(options = {}) {
    this.currentDate = options.currentDate || new Date().toISOString().split('T')[0];
    this.today = options.today || new Date().toISOString().split('T')[0];
    this.blockedDates = options.blockedDates || [];
    this.config = options.config || { skipWeekend: false, localTimeZone: 'GMT' };
  }

  /**
  * Validates the input tasks for conflicts.
  * @param {Array} tasks - List of task objects with their properties.
  * @returns {Object} - Returns an object with success status and conflicting task if any.
  */
  validate(event) {
    console.log("Validating tasks for conflicts...");
    const launchDate = this.getLaunchDate(event);
    const projectDepsMaxDate = this.getDepMaxDate(event.dependencies);
    const tasks = event.deliverables_list_custom_deliverable2;
    const checkDependenciesOverlap = this.checkDependenciesOverlap(tasks, launchDate);
    const checkFixedTaskOverlap = this.checkFixedTaskOverlap(tasks, launchDate);

    const validProjectDepsMaxDate = Boolean(projectDepsMaxDate);

    const runTopographyAlgo = sortTasksByDependency(tasks);


    // Check if any project dependency exceeds the launch date.
    if (validProjectDepsMaxDate) {
      if (projectDepsMaxDate > launchDate) {
        return { success: false, conflictingTask: event, message: `Conflict: A predeccesor dependency in activity setup exceeds start date(${launchDate.toDateString()}).` };
      }
    }

    // Check for predecessor dependency conflicts.
    if (checkDependenciesOverlap.overlap) {
      return { success: false, conflictingTask: checkDependenciesOverlap.task, message: `Conflict:A Predecessor dependency in deliverable "${checkDependenciesOverlap.task.name_text}" exceeds activity start date(${launchDate.toDateString()}).` };
    }

    // Check for fixed schedule conflicts in deliverables.
    if (checkFixedTaskOverlap.overlap) {
      return { success: false, conflictingTask: checkFixedTaskOverlap.task, message: `Conflict: Fixed schedule for deliverable "${checkFixedTaskOverlap.task.name_text}" overlaps with activity  start date(${launchDate.toDateString()}).` };
    }

    // Check for cyclic dependencies in the task list.
    if (!runTopographyAlgo.success) {
      return { success: false, conflictingTask: null, message: `Conflict: ${runTopographyAlgo.message}` };
    }

    return { success: true };
  }

  /**
   * Groups tasks into categories: nominal, and fixed.
   * @param {Array} tasks - List of task objects.
   * @returns {Object} - Grouped tasks by category.
   */
  orchestrate(tasks) {
    console.log("Grouping tasks into categories...");
    const groupedTasks = {
      parallel: [],
      nominal: [],
      fixed: []
    };

    for (let i = tasks.length - 1; i >= 0; i--) {
      const task = tasks[i];
      if (task.is_fixed) {
        groupedTasks.fixed.push(task);
        tasks.splice(i, 1); // Remove task from tasks array
      } else {
        groupedTasks.nominal.push(task);
        tasks.splice(i, 1); // Remove task from tasks array
      }
    }


    groupedTasks.nominal = this.orderAndBuildDAGgraph(groupedTasks.nominal);
    return groupedTasks;
  }

  /**
   * Calculates the schedules for tasks based on their groups.
   * @param {Object} groupedTasks - Tasks grouped by category.
   * @returns {Array} - List of calculated schedules.
   */
  calculate(groupedTasks) {
    console.log("Calculating schedules for tasks...");
    const schedules = [];
    //initilized blocked dates from fixed dates
    const blockedDates = this.deriveFixedDates(groupedTasks.fixed) // TODO include config blocked dates 

    let tasks = groupedTasks.nominal;

    const nominalTasks = planner(this.launchDate, tasks, groupedTasks.fixed);

    console.debug('Planned Tasks', nominalTasks);
    //TODO HANLDE CYCLIC CONFLICTS AND FIXED DATES
    const dates = nominalTasks.map(t => ({ _id: t._id, name_text: t.name_text, start: t.start, end: t.end, position: t.cursor }))

    // const fixedTasks = groupedTasks.fixed.map(fixedItem => [fixedItem]);
    // const parallelTasks = groupedTasks.parallel.map(parallelTask => this.calculateTaskDates([parallelTask]));

    // let result = this.mergeAndAssignPositions(nominalTasks, fixedTasks, parallelTasks);
    const validateResult = this.checkForPredecessorOverlaps(dates);


    if (!validateResult.overlap) {
      return dates;
    } else
      throw new Error(`Conflict detected: Overlapping schedules in dependencies. Please resolve. ${validateResult.task.name_text}`);
  }

  /**
 * Helper function to check for overlapping schedules (example logic).
 * @param {Object} task - Task to check.
 * @param {Array} tasks - List of tasks to compare against.
 * @returns {boolean} - True if overlap exists, false otherwise.
 */
  hasOverlap(task, tasks) {
    return tasks.some(otherTask => {
      if (otherTask === task || !otherTask.fixed) return false;

      const taskStartDate = this.parseToDate(task.startDate);
      const taskEndDate = this.parseToDate(task.endDate);
      const otherStartDate = this.parseToDate(otherTask.startDate);
      const otherEndDate = this.parseToDate(otherTask.endDate);

      if (!taskStartDate || !taskEndDate || !otherStartDate || !otherEndDate) {
        return false; // Skip tasks with invalid dates
      }

      return (
        taskStartDate < otherEndDate &&
        taskEndDate > otherStartDate
      );
    });
  }

  mergeAndAssignPositions(nominalTasks, fixedTasks, parallelTasks) {
    let positionCounter = 1; // Start position counter from 1 for fixedTasks and parallelTasks

    // Assign position 0 to all nominalTasks, if any
    const processedNominalTasks = (nominalTasks || []).map(task => ({
      ...task,
      position: 0,
    }));

    // Assign positions to fixedTasks (flatten them), if any
    const processedFixedTasks = (fixedTasks || []).flatMap(taskArray =>
      taskArray.map(task => ({
        ...task,
        position: positionCounter++,
      }))
    );

    // Assign positions to parallelTasks (flatten them), if any
    const processedParallelTasks = (parallelTasks || []).flatMap(taskArray =>
      taskArray.map(task => ({
        ...task,
        position: positionCounter++,
      }))
    );

    // Merge all tasks into a single array
    return [...processedNominalTasks, ...processedFixedTasks, ...processedParallelTasks];
  }

  calculateTaskDates(tasks, blockedDates) {
    // Convert dates to Date objects
    const launch = this.parseToDate(this.launchDate)
    const blocked = (blockedDates || []).map(date => this.parseToDate(date));

    // Helper function to check if a date is blocked
    const isBlockedDate = (date) => blocked.some(blockedDate => blockedDate.getTime() === date.getTime());

    // Helper function to subtract a number of days, skipping blocked dates
    function subtractDaysSkippingBlocked(startDate, days) {
      const dates = [];
      let currentDate = startDate;

      while (dates.length < days) {
        currentDate.setDate(currentDate.getDate() - 1); // Subtract a day
        if (!isBlockedDate(currentDate)) {
          dates.push(new Date(currentDate)); // Add only non-blocked dates
        }
      }

      return dates.reverse(); // Return in ascending order
    }

    // Calculate dates for each task in reverse order
    let currentEndDate = launch;

    tasks.reverse().forEach(task => {
      const taskDays = subtractDaysSkippingBlocked(currentEndDate, task.duration);
      task.start = taskDays[0]; // Keep as Date object
      task.end = taskDays[taskDays.length - 1]; // Keep as Date object
      currentEndDate = new Date(taskDays[0]);
      currentEndDate.setDate(currentEndDate.getDate()); // Move to the day before the new start date
    });

    // Return the tasks in the original order
    return tasks.reverse();
  }

  orderAndBuildDAGgraph(tasks) {
    //sort the tasks by name_text from a - z
    //sort by duration: approval_time_number + time_period_number
    //sort by priority level from p1-p5, p1 being highest
    let sortedTasks = this.sortTasks(tasks);
    const t = sortTasksByDependency(sortedTasks)
    return t.tasks;
  }

  sortTasks(tasks) {
    const priorityToNumber = (priority) => {
      const priorityMap = {
        'Critical (P1)': 1,
        'High (P2)': 2,
        'Medium (P3)': 3,
        'Low (P4)': 4,
        'Negligible (P5)': 5
      };
      return priorityMap[priority] || 3; // Default to Medium (P3) for unknown priorities
    };

    return [...tasks].sort((a, b) => {
      // 1. Sort by priority first (P1 to P5)
      const priorityA = priorityToNumber(a.priority_option);
      const priorityB = priorityToNumber(b.priority_option);
      if (priorityA !== priorityB) return priorityA - priorityB;

      // 2. Sort by duration (higher duration first)
      const durationA = a.approval_time_number + a.time_period_number;
      const durationB = b.approval_time_number + b.time_period_number;
      if (durationA !== durationB) return durationB - durationA; // Note: reversed for descending order

      // 3. Sort by name_text alphabetically
      // return a.name_text.localeCompare(b.name_text);
    });
  }

  deriveFixedDates(tasksWithFixedDate) {
    if (!Array.isArray(tasksWithFixedDate)) {
      return [];
    }

    // Get all valid fixed date ranges
    const allDates = tasksWithFixedDate.reduce((dates, task) => {
      if (task?.is_fixed && task?.start && task?.end) {
        const startDate = new Date(task.start);
        const endDate = new Date(task.end);

        // Skip invalid dates
        if (isNaN(startDate) || isNaN(endDate) || startDate > endDate) {
          return dates;
        }

        // Set to start of day in UTC
        startDate.setUTCHours(0, 0, 0, 0);
        endDate.setUTCHours(0, 0, 0, 0);

        // Add each date in range
        const currentDate = new Date(startDate);
        while (currentDate <= endDate) {
          dates.push(new Date(currentDate).toISOString());
          currentDate.setUTCDate(currentDate.getUTCDate() + 1);
        }
      }
      return dates;
    }, []);

    // Remove duplicates and sort chronologically
    return [...new Set(allDates)].sort();
  }

  mergeAndAssignPositions(nominalTasks, fixedTasks, parallelTasks) {
    let positionCounter = 1; // Start position counter from 1 for fixedTasks and parallelTasks

    // Assign position 0 to all nominalTasks, if any
    const processedNominalTasks = (nominalTasks || []).map(task => ({
      ...task,
      position: 0,
    }));

    // Assign positions to fixedTasks (flatten them), if any
    const processedFixedTasks = (fixedTasks || []).flatMap(taskArray =>
      taskArray.map(task => ({
        ...task,
        position: positionCounter++,
      }))
    );

    // Assign positions to parallelTasks (flatten them), if any
    const processedParallelTasks = (parallelTasks || []).flatMap(taskArray =>
      taskArray.map(task => ({
        ...task,
        position: positionCounter++,
      }))
    );

    // Merge all tasks into a single array
    return [...processedNominalTasks, ...processedFixedTasks, ...processedParallelTasks];
  }

  /**
  * Function to find the maximum date from a list of objects based on specific criteria:
  * - The item must have `is_external_dep` set to `true`.
  * - The `type` must be `"predecessor"`.
  * - Considers `obj.start_date_date` and `obj.start` as date candidates.
  * - Handles undefined, null, or missing values gracefully.
  * 
  * @param {Array} deps - The list of objects to process.
  * @returns {string|null} - The ISO string of the latest date, or null if no valid date is found.
  */
  getDepMaxDate(deps) {
    if (!Array.isArray(deps) || deps.length === 0) {
      return null; // Handle empty array or invalid input
    }

    let maxDate = null;

    deps.forEach(item => {
      if (item.is_dependent_into && item.type === "predecessor") {
        const startDate = this.parseToDate(item.obj?.start_date_date);
        const start = this.parseToDate(item.obj?.start);

        const candidateDate = startDate && start
          ? new Date(Math.max(startDate.getTime(), start.getTime())) // Compare the two dates
          : startDate || start; // Use the available date if only one exists

        if (candidateDate) {
          maxDate = maxDate
            ? new Date(Math.max(maxDate.getTime(), candidateDate.getTime()))
            : candidateDate;
        }
      }
    });

    return maxDate ? this.parseToDate(maxDate.toISOString()) : null; // Return ISO string of maxDate or null
  }

  /**
  * Function to check if any task's dependencies overlap with the launch date.
  * 
  * @param {Array} tasks - List of tasks to analyze.
  * @param {string} launchDate - The launch date in ISO string format or other valid format.
  * @returns {Object} - An object containing `overlap` (boolean) and `task` (the first task with overlap or null).
  */
  checkDependenciesOverlap(tasks, launchDate) {
    if (!Array.isArray(tasks) || !launchDate) {
      throw new Error("Invalid input. Provide a valid list of tasks and a launch date.");
    }

    const launchDateTime = this.parseToDate(launchDate)
    if (!launchDateTime) {
      throw new Error("Invalid launch date format.");
    }

    for (const task of tasks) {
      if (Array.isArray(task.dependencies_list_custom_dependency)) {
        const maxDate = this.getDepMaxDate(task.dependencies_list_custom_dependency);
        const validDate = Boolean(maxDate);
        if (maxDate && maxDate >= launchDateTime) {
          return { overlap: true, task };
        }
      }
    }

    return { overlap: false, task: null };
  }

  checkForPredecessorOverlaps(tasks) {

    const getMaxDate = (deps) => {
      if (!Array.isArray(deps) || deps.length === 0) {
        return null; // Handle empty array or invalid input
      }

      let maxDate = null;

      deps.forEach(item => {
        if (item.is_dependent_into && item.type === "successor") {
          const startDate = this.parseToDate(item.obj?.start_date_date);
          const start = this.parseToDate(item.obj?.start);

          const candidateDate = startDate && start
            ? new Date(Math.max(startDate.getTime(), start.getTime())) // Compare the two dates
            : startDate || start; // Use the available date if only one exists

          if (candidateDate) {
            maxDate = maxDate
              ? new Date(Math.max(maxDate.getTime(), candidateDate.getTime()))
              : candidateDate;
          }
        }
      });

      return maxDate ? this.parseToDate(maxDate.toISOString()) : null; // Return ISO string of maxDate or null
    }


    const overlappingTask = tasks.find(task => {
      const startDate = this.parseToDate(task.start),
        endDate = this.parseToDate(task.end);
      const maxDepDate = getMaxDate(task.dependencies_list_custom_dependency);

      return maxDepDate && (maxDepDate >= startDate || maxDepDate >= endDate);
    });

    if (overlappingTask) {
      return { overlap: true, task: overlappingTask };
    }



    return { overlap: false, task: null };
  }

  /**
  * Function to check if any task with fixed dates overlaps with the launch date.
  * 
  * @param {Array} tasks - List of tasks to analyze.
  * @param {string} launchDate - The launch date in ISO string format or other valid format.
  * @returns {Object} - An object containing `overlap` (boolean) and `task` (the first task with overlap or null).
  */
  checkFixedTaskOverlap(tasks, launchDate) {
    if (!Array.isArray(tasks) || !launchDate) {
      throw new Error("Invalid input. Provide a valid list of tasks and a launch date.");
    }


    if (!launchDate) {
      throw new Error("Invalid launch date format.");
    }

    for (const task of tasks) {
      const startDate = this.parseToDate(task.start);
      const endDate = this.parseToDate(task.end);

      if (task.is_fixed && startDate && endDate) {


        if (startDate >= launchDate || endDate >= launchDate) {
          return { overlap: true, task };
        }
      }
    }

    return { overlap: false, task: null };
  }

  /**
  * Utility function to parse a date string or ISO format into a JavaScript Date object.
  * Returns `undefined` for invalid inputs.
  * 
  * @param {string} input - The date string to parse.
  * @returns {Date|undefined} - Parsed Date object or undefined if invalid.
  */
  parseToDate(input) {
    // If it's in DD/MM/YY or DD/MM/YYYY format
    if (/^\d{1,2}\/\d{1,2}\/(\d{2}|\d{4})$/.test(input)) {
      const [day, month, yearInput] = input.split('/').map(Number);

      // Convert 2-digit year to full year
      const year = yearInput < 100
        ? yearInput + (yearInput >= 50 ? 1900 : 2000)
        : yearInput;

      const date = new Date(year, month - 1, day); // month is 0-based

      // Validate the date is correct
      if (date.getDate() !== day || date.getMonth() + 1 !== month || date.getFullYear() !== year) {
        return undefined;
      }

      return date;
    }

    // If it's in ISO format, convert it
    const isoDate = new Date(input);
    if (!isNaN(isoDate.getTime())) {
      return isoDate;
    }

    return undefined;
  }

  getLaunchDate(event) {
    let startDate = new Date(this.parseToDate(event.start_date_date)), workDiffDate = event.approval_time_number || 0;
    return new Date(startDate.setDate(startDate.getDate() - workDiffDate));
  }

  hasNoInternalDep(task) {
    // Return true if there are no dependencies
    if (!task.dependencies_list_custom_dependency || task.dependencies_list_custom_dependency.length === 0) {
      return true;
    }

    // Check if any dependency has is_external_dep as true
    for (let dependency of task.dependencies_list_custom_dependency) {
      if (!dependency.is_external_dep) {
        return false;
      }
    }

    // Return true if no external dependencies exist
    return true;
  }

  /**
 * Processes deliverables successor dependencies by checking and updating dependencies.
 * Converts "successor" dependencies to "predecessor" in the source deliverable,
 * switches source_id and target_id, and removes the processed dependency.
 */
  processTasksSuccessorDeps(deliverables) {
    function cloneObject(obj) {
      return JSON.parse(JSON.stringify(obj));
    }
    deliverables.forEach((deliverable) => {
      const updatedDependencies = [];

      deliverable.dependencies_list_custom_dependency.forEach((dependency) => {
        if (
          dependency.type === "successor" &&
          dependency.is_dependent_into === false &&
          dependency.data_type === "deliverable"
        ) {
          const sourceDeliverable = deliverables.find(
            (d) => d._id === dependency.target_id
          );

          if (sourceDeliverable) {
            const clonedDependency = cloneObject(dependency);

            clonedDependency.source_id = dependency.target_id;
            clonedDependency.target_id = dependency.source_id;
            clonedDependency.type = "predecessor";
            clonedDependency.obj = deliverable;

            sourceDeliverable.dependencies_list_custom_dependency.push(
              clonedDependency
            );
          }
        } else {
          updatedDependencies.push(dependency);
        }
      });

      deliverable.dependencies_list_custom_dependency = updatedDependencies;
    });

    return deliverables;
  }

  invertTasksDeps(deliverables) {
    function cloneObject(obj) {
      return JSON.parse(JSON.stringify(obj));
    }

    deliverables.forEach((deliverable) => {
      const updatedDependencies = [];

      deliverable.dependencies_list_custom_dependency.forEach((dependency) => {
        if (
          dependency.type === 'predecessor' &&
          dependency.is_dependent_into === false &&
          dependency.data_type === "deliverable"
        ) {
          const sourceDeliverable = deliverables.find(
            (d) => d._id === dependency.source_id
          );

          if (sourceDeliverable) {
            const clonedDependency = cloneObject(dependency);

            clonedDependency.source_id = dependency.target_id;
            clonedDependency.target_id = dependency.source_id;
            clonedDependency.type = "successor";
            clonedDependency.obj = deliverable;

            sourceDeliverable.dependencies_list_custom_dependency.push(
              clonedDependency
            );
          }
        } else {
          updatedDependencies.push(dependency);
        }
      });

      deliverable.dependencies_list_custom_dependency = updatedDependencies;
    });

    return deliverables;
  }

  /**
   * Main function to execute the reverse scheduling algorithm.
   * @param {Array} tasks - List of task objects.
   * @returns {Object} - Result containing success status and schedules or error.
   */
  execute(activity) {
    // 1. Initialized and process tasks
    let tasks = activity.deliverables_list_custom_deliverable2;
    this.launchDate = this.getLaunchDate(activity);
    tasks = this.processTasksSuccessorDeps(tasks)

    let inverted = this.invertTasksDeps(tasks)

    // Set event deliverables to process task
    activity.deliverables_list_custom_deliverable2 = inverted;

    //2. Validate tasks for possible schedules
    const validation = this.validate(activity);
    if (!validation.success) {
      return { success: false, error: validation.message, conflictingTask: validation.conflictingTask };
    }

    //TODO Prepare data for calculation


    //3: Orchestrate tasks
    const groupedTasks = this.orchestrate(tasks);
    // Step 3: Calculate tasks schedules
    try {
      const schedules = this.calculate(groupedTasks);
      return { success: true, schedules };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Static property for the live date of the algorithm.
   */
  static get liveDate() {
    return "2024-12-26";
  }
}

const runCalculation = ((data)=>{
  const scheduler = new ReverseSchedulingAlgorithm;
  const schedule = scheduler.execute(data);
  return schedule;
})

module.exports = {runCalculation};