const fetch = require("node-fetch");

/**
 * Generic Fetch API function
 * @param {string} url - The API endpoint
 * @param {string} type - HTTP method (GET, POST, PATCH)
 * @param {string} authorization - Authorization token (Bearer or other type)
 * @param {Object} [body] - Optional request body for POST or PATCH
 * @returns {Promise<Object>} - The JSON response or an error
 */
async function fetchAPI(url, type, authorization, body = null) {
    try {
        const options = {
            method: type.toUpperCase(), // Ensure method is in uppercase
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${authorization}`, // Add Authorization header
            },
        };

        // Include body for POST & PATCH requests
        if (body && (type === "POST" || type === "PATCH")) {
            options.body = JSON.stringify(body);
        }

        // Perform the fetch request
        const response = await fetch(url, options);

        // Handle non-OK responses
        if (!response.ok) {
            throw new Error(`HTTP Error ${response.status}: ${response.statusText}`);
        }

        return await response.json(); // Parse and return JSON response
    } catch (error) {
        return { success: false, message: error.message };
    }
}

module.exports = fetchAPI;
