import { preparedData as buildObject } from "./core/prepareData";
import { runCalculation } from './core/planner';



async function processData(input) {
    try {
        // Step 1: Prepare Data
        const preparedData = await buildObject(input);
        
        // Step 2: Run Calculation
        const calculationResult =  runCalculation(preparedData);

        // If calculation fails, return an error response
        // if (!calculationResult.success) {
        //     return { success: false, message: calculationResult.errorMessage };
        // }

        // Step 3: Update Database
        // await updateDatabase(calculationResult.data);

        // Step 4: Return Success Response
        return { success: true, message: "Operation completed successfully", data:  calculationResult};
    
    } catch (error) {
        // Catch any unexpected errors
        return { success: false, message: `Unexpected error: ${error.message}` };
    }
}


module.exports = { processData };
