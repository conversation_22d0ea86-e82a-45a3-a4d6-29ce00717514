
async function(properties, context) {

async function unWrapActivities(inputList) {
    const listLength = await inputList.length();
    const items = await inputList.get(0, listLength);
    
    return await Promise.all(items.map(async (item) => {
        const ele = await deepUnwrap(item);
        return ele;
    }));
}

async function unWrapDeliverable(inputList) {
    const listLength = await inputList.length();
    const items = await inputList.get(0, listLength);

    return await Promise.all(items.map(async (item) => {
        const ele = await extractDeliverableData(item);
        ele.deps = await unWrapDependencies(ele.deps);
        return ele;
    }));
}

async function extractDeliverableData(item) {
    return {
        _id: await item.get('_id'),
        name_text: await item.get('name_text'),
        start: await item.get('start_date_date'),
        end: await item.get('end_date_date'),
        deps: await item.get('dependencies_list_custom_dependency')
    };
}

async function unWrapDependencies(dependencies) {
    if (!dependencies) return [];
    const depLength = dependencies.length;
    const depItems = await dependencies.get(0, depLength);

    if (!Array.isArray(depItems)) return [];

    const openedDeps = await Promise.all(depItems.map(async (depItem) => {
        return await extractDependencyData(depItem);
    }));

    return openedDeps.filter(Boolean);
}

async function extractDependencyData(depItem) {
    const depItemLoadedObj = await depItem.getAll();
    if (!depItemLoadedObj) return null;

    return {
        _id: await depItem.get('_id'),
        is_dependent_into_boolean: await depItem.get('is_dependent_into_boolean') ?? false,
        type: depItemLoadedObj.type_text ?? null,
        source_id: depItemLoadedObj.source_id_text?._pointer?._id ?? null,
        target_id: depItemLoadedObj.target_id_text?._pointer?._id ?? null,
        obj: await resolveDependencyObject(depItemLoadedObj)
    };
}

async function deepUnwrap(activity) {
    if (!activity) throw new Error("Invalid Activity object");

    const deliverables = await unWrapDeliverable(await activity.get('deliverables_list_custom_deliverable2'));
    const dependencies = await unWrapDependencies(await activity.get('dependencies_list_custom_dependency'));

    return {
        _id: await activity.get('_id'),
        name_text: await activity.get('name_text'),
        deliverables_list_custom_deliverable2: deliverables,
        dependencies: dependencies,
        start_date_date: await activity.get('start_date_date'),
        approval_time_number: await activity.get('approval_time_number')
    };
}

async function resolveDependencyObject(depItem) {
    const obj = depItem.activity_custom_activity1 || depItem.deliverable_custom_deliverable2;
    if (!obj || !obj._pointer?._source) return null;

    const source = obj._pointer._source;
    return depItem.activity_custom_activity1 ? {
        _id: source._id,
        name_text: source.name_text,
        start_date_date: source.start_date_date
    } : {
        _id: source._id,
        name_text: source.name_text,
        start: source.start_date_date,
        end: source.end_date_date
    };
}

// Execution
var activity = properties.activity;
var result = await deepUnwrap(activity);
    


    // return {
    //     success: true,
    //     message: JSON.stringify(''),
    //     error: '',
    //     data: JSON.stringify(result)
    // }
    console.debug(object);
}