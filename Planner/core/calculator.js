function planner(launchDate = new Date(), tasks = [], fixedTasks = []) {
  const INITIAL_CURSOR = 0;
  const INITIAL_ROW = 0;
  const visited = new Map();
  let currentRow = INITIAL_ROW;

  const sanitizedTasks = sanitizeTasks(tasks);

  // Process fixed tasks
  if (fixedTasks.length > 0) {
      const sanitizedFixedTasks = sanitizeTasksForFixed(fixedTasks);
      for (const task of sanitizedFixedTasks) {
          const overlappedTask = canOverlapWithOtherTasks(task, visited);
          task.cursor = overlappedTask ? overlappedTask.cursor + 1 : INITIAL_CURSOR;
          visited.set(task._id, task);
      }
  }

  // Process dynamic tasks
  for (let i = 0; i < sanitizedTasks.length; i++) {
      const task = sanitizedTasks[i];
      const farthestDep = getFarthestDependency(task.deps, sanitizedTasks);
      const currentVisitedRow = filterMapByRow(visited, currentRow);
      const lastEntry = getLastEntry(visited);

      // Initial task with no dependencies
      if (!visited.has(task._id) && (!task.deps?.length) && visited.size === 0) {
          scheduleInitialTask(task, launchDate, INITIAL_CURSOR, INITIAL_ROW);
          visited.set(task._id, task);
          continue;
      }

      // Parallel task with dependency in current row, no successors
      if (farthestDep && task.is_parallel && currentVisitedRow.has(farthestDep) && 
          currentVisitedRow.get(farthestDep).visitedSucessorTasks.size === 0) {
          currentRow++;
          scheduleParallelTask(task, currentVisitedRow, INITIAL_CURSOR, currentRow);
          visited.set(task._id, task);
          continue;
      }

      // Parallel task with dependency not in current row, last entry is parallel
      if (farthestDep && task.is_parallel && !currentVisitedRow.has(farthestDep) && 
          lastEntry?.value.is_parallel) {
          scheduleParallelSuccessor(task, lastEntry.value);
          visited.set(task._id, task);
          continue;
      }

      // Sequential task
      if (!task.is_parallel || (lastEntry && !lastEntry.value.is_parallel)) {
          if (lastEntry) {
              currentRow++;
              scheduleSequentialTask(task, currentVisitedRow, currentRow);
              visited.set(task._id, task);
              continue;
          }
      }

      // Parallel task with no dependencies
      if (task.is_parallel && (!task.deps?.length)) {
          scheduleParallelSuccessor(task, lastEntry.value);
          visited.set(task._id, task);
          continue;
      }
  }

  return sanitizedTasks;
}

// Helper Functions
function scheduleInitialTask(task, launchDate, cursor, row) {
  task.cursor = cursor;
  task.end = launchDate;
  task.start = addDays(launchDate, task.duration);
  task.row = row;
}

function scheduleParallelTask(task, currentVisitedRow, cursor, row) {
  task.cursor = cursor;
  task.row = row;
  task.end = addDays(getMinDateFromMap(currentVisitedRow), 1);
  task.start = addDays(task.end, task.duration);
}

function scheduleParallelSuccessor(task, predecessor) {
  const successorTasks = predecessor.visitedSucessorTasks;
  const lastSuccessor = successorTasks.size > 0 
      ? Array.from(successorTasks.values()).slice(-1)[0] 
      : null;
  
  task.cursor = lastSuccessor ? lastSuccessor.cursor + 1 : predecessor.cursor + 1;
  task.end = lastSuccessor ? lastSuccessor.end : predecessor.end;
  task.start = addDays(task.end, task.duration);
  task.row = predecessor.row;
  predecessor.visitedSucessorTasks.set(task._id, task);
}

function scheduleSequentialTask(task, currentVisitedRow, row) {
  task.cursor = 0;
  task.end = addDays(getMinDateFromMap(currentVisitedRow), 1);
  task.start = addDays(task.end, task.duration);
  task.row = row;
}

function getFarthestDependency(depList, tasks) {
  if (!depList?.length) return null;
  const taskIds = tasks.map(task => task._id);
  return depList.sort((a, b) => taskIds.indexOf(b) - taskIds.indexOf(a))[0];
}

function sanitizeTasks(tasks) {
  if (!tasks?.length) return [];
  return tasks.map(task => ({
      _id: task._id,
      name_text: task.name_text,
      duration: task.duration,
      deps: (task.dependencies_list_custom_dependency || [])
          .filter(dep => dep?.type === 'successor' && 
                        !dep.is_dependent_into && 
                        dep.data_type === 'deliverable')
          .map(dep => dep.obj._id),
      is_parallel: task.is_parallel || false,
      priority_option: task.priority_option,
      start: undefined,
      end: undefined,
      cursor: undefined,
      row: undefined,
      col: undefined,
      visitedSucessorTasks: new Map()
  }));
}

function sanitizeTasksForFixed(tasks) {
  if (!tasks?.length) return [];
  return tasks.map(task => ({
      ...sanitizeTasks([task])[0],
      start: task.start,
      end: task.end
  }));
}

function canOverlapWithOtherTasks(task, visited) {
  for (const visitedTask of visited.values()) {
      if ((task.start >= visitedTask.start && task.start <= visitedTask.end) ||
          (task.end >= visitedTask.start && task.end <= visitedTask.end) ||
          (visitedTask.start >= task.start && visitedTask.start <= task.end)) {
          return visitedTask;
      }
  }
  return null;
}

function addDays(date, days) {
  const newDate = new Date(date);
  newDate.setDate(date.getDate() - days);
  return newDate;
}

function filterMapByRow(map, rowValue) {
  return new Map([...map.entries()].filter(([, value]) => value.row === rowValue));
}

function getLastEntry(map) {
  if (map.size === 0) return null;
  const [key, value] = [...map.entries()].pop();
  return { key, value };
}

function getMinDateFromMap(tasksMap) {
  if (!(tasksMap instanceof Map) || tasksMap.size === 0) return null;
  
  let minDate = null;
  for (const task of tasksMap.values()) {
      const startDate = task.start instanceof Date ? task.start : new Date(task.start);
      const endDate = task.end instanceof Date ? task.end : new Date(task.end);
      
      if (!isNaN(startDate) && (minDate === null || startDate < minDate)) {
          minDate = startDate;
      }
      if (!isNaN(endDate) && (minDate === null || endDate < minDate)) {
          minDate = endDate;
      }
  }
  return minDate;
}

// module.exports = planner;