// const graphlib = require('graphlib');

function sortTasksByDependency(tasks) {
    const graph = new graphlib.Graph({ directed: true, compound: true, multigraph: true });
  
    // Add all tasks as nodes first
    tasks.forEach(task => {
      graph.setNode(task._id, task);
    });
  
    // Track all dependencies for validation
    const dependencyMap = new Map();
  
    // Add edges for all dependencies
    tasks.forEach(task => {
      if (task.dependencies_list_custom_dependency && Array.isArray(task.dependencies_list_custom_dependency)) {
        // Group dependencies by target_id
        const dependenciesByTarget = task.dependencies_list_custom_dependency.reduce((acc, dep) => {
          if (dep.type === 'successor' && !dep.is_dependent_into) {
            if (!acc[dep.target_id]) {
              acc[dep.target_id] = [];
            }
            acc[dep.target_id].push(dep.source_id);
          }
          return acc;
        }, {});
  
        // For each target, add edges from all its sources
        Object.entries(dependenciesByTarget).forEach(([targetId, sourceIds]) => {
          sourceIds.forEach(sourceId => {
            graph.setEdge(sourceId, targetId);
  
            // Store in dependency map for validation
            if (!dependencyMap.has(targetId)) {
              dependencyMap.set(targetId, new Set());
            }
            dependencyMap.get(targetId).add(sourceId);
          });
        });
      }
    });
  
    try {
  
      const sortedIds = graphlib.alg.topsort(graph);
      const sortedTasks = sortedIds.map(id => graph.node(id));
  
      // Validate that all dependencies are satisfied
      const validationResult = validateSorting(sortedTasks, dependencyMap);
      if (!validationResult.valid) {
        return {
          success: false,
          tasks: [],
          message: validationResult.message
        };
      }
  
      return {
        success: true,
        tasks: sortedTasks,
        message: 'Tasks sorted successfully'
      };
  
    } catch (error) {
      const a = error
      return {
        success: false,
        tasks: [],
        message: 'Cyclic dependency detected in task relationships'
      };
    }
  }
  
  function validateSorting(sortedTasks, dependencyMap) {
    const taskIndexMap = new Map(sortedTasks.map((task, index) => [task._id, index]));
  
    for (const [taskId, predecessors] of dependencyMap) {
      const taskIndex = taskIndexMap.get(taskId);
  
      for (const predecessorId of predecessors) {
        const predecessorIndex = taskIndexMap.get(predecessorId);
  
        if (predecessorIndex === undefined) {
          return {
            valid: false,
            message: `Missing predecessor task with ID: ${predecessorId}`
          };
        }
  
        if (predecessorIndex > taskIndex) {
          const task = sortedTasks.find(t => t._id === taskId);
          const predecessor = sortedTasks.find(t => t._id === predecessorId);
          return {
            valid: false,
            message: `Invalid sorting:Tasks sorted successfully Task "${task.name_text}" appears before its predecessor "${predecessor.name_text}"`
          };
        }
      }
    }
  
    return {
      valid: true,
      message: 'Sorting is valid'
    };
  }
  
  // module.exports = {
  
  //   sortTasksByDependency,
  //   validateSorting
  // };