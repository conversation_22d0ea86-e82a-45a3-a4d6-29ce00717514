<script src="//meta-q.cdn.bubble.io/f1743374733614x279691069223318980/timelineCore.js"></script>
<script src="https://cdn.jsdelivr.net/npm/rrule@2.7.2/dist/es5/rrule.min.js"></script>
<script>window.RRule = window.RRule || rrule.RRule;</script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/luxon/3.5.0/luxon.js"></script>
<script>
function generateMaterialLiveDates(params) {
  // Validate required parameters
  const requiredParams = ['activityStartDate', 'activityEndDate', 'type', 'duration'];
  for (const param of requiredParams) {
      if (!params[param]) {
          throw new Error(`Missing required parameter: ${param}`);
      }
  }
  
  // Parse dates and duration
  const { DateTime } = luxon;
  const activityStart = DateTime.fromISO(params.activityStartDate);
  const activityEnd = DateTime.fromISO(params.activityEndDate.replace(/'/g, ''));
  const duration = parseInt(params.duration);
  
  // Initialize result
  const result = {
      liveDates: [],
      hasExceedingDates: false,
      exceededDates: []
  };
  
  // Calculate initial start date based on type
  let startDate;
  if (params.type === 'start_with_activity') {
      startDate = activityStart;
  } else if (params.type === 'start_after_activity') {
      if (!params.period_number || !params.period_type) {
          throw new Error('Missing period_number or period_type for start_after_activity');
      }
      
      const periodNumber = parseInt(params.period_number);
      const periodType = params.period_type;
      
      // Handle different period types
      if (['day', 'week', 'month', 'year'].includes(periodType)) {
          // For standard time periods, add the number to the activity start date
          const periodTypePlural = periodType.endsWith('s') ? periodType : `${periodType}s`;
          startDate = activityStart.plus({ [periodTypePlural]: periodNumber });
      } else if (['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].includes(periodType)) {
          // For weekdays, find the nth occurrence of that weekday after the activity start
          const weekdayMap = {
              'monday': 1,
              'tuesday': 2,
              'wednesday': 3,
              'thursday': 4,
              'friday': 5,
              'saturday': 6,
              'sunday': 7
          };
          
          const targetWeekday = weekdayMap[periodType];
          let currentDate = activityStart;
          let occurrenceCount = 0;
          
          // Find the first occurrence of the weekday
          while (currentDate.weekday !== targetWeekday) {
              currentDate = currentDate.plus({ days: 1 });
          }
          
          occurrenceCount++;
          
          // Find the nth occurrence
          while (occurrenceCount < periodNumber) {
              currentDate = currentDate.plus({ weeks: 1 });
              occurrenceCount++;
          }
          
          startDate = currentDate;
      } else {
          throw new Error(`Invalid period type: ${periodType}`);
      }
  } else {
      throw new Error(`Invalid type: ${params.type}`);
  }
  
  // Handle repetition
  const dates = [];
  if (params.repeat === 'yes') {
      const repeatType = params.repeat_type || 'weekly';
      const repeatFrequency = parseInt(params.repeat_frequency || '1');
      
      if (repeatType === 'yearly') {
          // Handle yearly repetition manually since RRule might not handle it as expected
          let currentYear = startDate.year;
          let currentDate = startDate;
          
          while (currentDate <= activityEnd) {
              // Add the current date range
              const end = currentDate.plus({ days: duration - 1 });
              dates.push({ start: currentDate, end });
              
              // Move to the next year based on frequency
              currentYear += repeatFrequency;
              currentDate = currentDate.set({ year: currentYear });
          }
      } else {
          // For other repetition types, use RRule
          const RRule = window.RRule;
          const frequencyMap = {
              'daily': RRule.DAILY,
              'weekly': RRule.WEEKLY,
              'monthly': RRule.MONTHLY
          };
          
          // Create rule for repetition
          const rule = new RRule({
              freq: frequencyMap[repeatType] || RRule.WEEKLY,
              interval: repeatFrequency,
              dtstart: startDate.toJSDate(),
              until: activityEnd.toJSDate()
          });
          
          // Generate all start dates
          const startDates = rule.all();
          
          // Create date ranges
          for (const date of startDates) {
              const start = DateTime.fromJSDate(date);
              const end = start.plus({ days: duration - 1 });
              dates.push({ start, end });
          }
      }
  } else {
      // Just one date range
      const end = startDate.plus({ days: duration - 1 });
      dates.push({ start: startDate, end });
  }
  
  // Process date ranges and check for exceeding dates
  for (const { start, end } of dates) {
      const dateRange = {
          start: start.toISODate(),
          end: end.toISODate()
      };
      
      const dateArray = [new Date(start.toJSDate()), new Date(end.toJSDate())];
      if (end > activityEnd) {
          result.hasExceedingDates = true;
          result.exceededDates.push(dateRange);
      } else {
          result.liveDates.push(dateArray);
      }
  }
  
  return result;
}
</script>

