
//Represent the core algortihims of this library
function planner(launchDate = Date.now(), tasks, fixedTasks = []) {
  const cursor = 0, row = 0,
    visited = new Map();
  let currentRow = 0;
  debugger
  // launchDate = launchDate || Date.now();

  tasks = sanitizeTasks(tasks);

  //Planned 
  if (fixedTasks.length >= 1) {
    fixedTasks = sanitizeTasksForFixed(fixedTasks);
    for (let i = 0; i < fixedTasks.length; i++) {
      const task = fixedTasks[i];
      const overlappedTask = canOverlapWithOtherTasks(task, visited)
      if (overlappedTask) {
        task.cursor = overlappedTask.cursor + 1
      } else if (!overlappedTask) {
        task.cursor = cursor
      }
      visited.set(task._id, task); // Mark task as visited
      continue;
    }
  }


  // Process tasks in the correct order
  for (let index = 0; index < tasks.length; index++) {
    const task = tasks[index];

    const farthestDep = getFarthestDependency(task.deps, tasks);
    //lastVisitEDrOW = return items of the currentRow
    let currentVisitedRow = filterMapByRow(visited, currentRow);
    let lastEntry = getLastEntry(visited);



    // If task is not in visited and task.dependencies is empty and visited is empty
    if (!visited.has(task._id) && (!task.deps || task.deps.length === 0) && visited.size === 0) {
      task.cursor = cursor;
      task.end = launchDate;
      task.start = addDays(launchDate, task.duration); // Backward schedule
      task.row = row;

      visited.set(task._id, task); // Mark task as visited

      continue;
    }


    if (farthestDep && task.is_parallel && currentVisitedRow.get(farthestDep) && (currentVisitedRow.get(farthestDep).visitedSucessorTasks.size === 0)) {
      currentRow++;
      task.cursor = cursor;
      task.row = currentRow
      task.end = addDays(getMinDateFromMap(currentVisitedRow), 1); 
      task.start = addDays(task.end, task.duration);
      // currentVisitedRow.get(farthestDep).visitedSucessorTasks.set(task._id, task); // Add to successor tasks
      visited.set(task._id, task);

      continue;
    }


    // do logi
    if (farthestDep && task.is_parallel && !currentVisitedRow.get(farthestDep) && lastEntry.value.is_parallel) {
      if (lastEntry.value.visitedSucessorTasks.size === 0) { //TODO this will always be true
        task.cursor = lastEntry.value.cursor + 1; // Increment cursor based on last successor

        task.end = lastEntry.value.end
        task.start = addDays(task.end, task.duration);
        task.row = lastEntry.value.row;
        lastEntry.value.visitedSucessorTasks.set(task._id, task);

      } else {

        const lastSuccessor = Array.from(
          lastEntry.value.visitedSucessorTasks.values()
        ).slice(-1)[0];

        task.cursor = lastSuccessor.cursor + 1; // Increment cursor based on last successor

        task.end = lastSuccessor.end
        task.start = addDays(task.end, task.duration);
        task.row = lastEntry.value.row;

        lastEntry.value.visitedSucessorTasks.set(task._id, task)
      }

      visited.set(task._id, task);

      continue;
    }

    // If task is_parallel is false and not dependent
    if (!task.is_parallel || !lastEntry.value.is_parallel) {

      if (lastEntry) {
        currentRow++
        task.cursor = 0; // Set the cursor as 0
        task.end = addDays(getMinDateFromMap(currentVisitedRow), 1); 
        task.start = addDays(task.end, task.duration);
        task.row = currentRow

        visited.set(task._id, task);

        continue;
      }

    }

    // If task is_parallel and has no dependencies
    if (task.is_parallel && (!task.deps || task.deps.length === 0)) {
      if (lastEntry.value.visitedSucessorTasks.size === 0) {
        task.cursor = lastEntry.value.cursor + 1; // Increment cursor based on last successor

        task.end = lastEntry.value.end
        task.start = addDays(task.end, task.duration);
        task.row = lastEntry.value.row;
        lastEntry.value.visitedSucessorTasks.set(task._id, task);

      } else {

        const lastSuccessor = Array.from(
          lastEntry.value.visitedSucessorTasks.values()
        ).slice(-1)[0];

        task.cursor = lastSuccessor.cursor + 1; // Increment cursor based on last successor

        task.end = lastSuccessor.end
        task.start = addDays(task.end, task.duration);
        task.row = lastEntry.value.row;

        lastEntry.value.visitedSucessorTasks.set(task._id, task)
      }

      visited.set(task._id, task);

      continue;
    }

  }

  return tasks;
}

// Helper function to get the farthest dependency based on position in the list
function getFarthestDependency(depList, tasks) {
  if (!depList || depList.length === 0) return null;
  const listIds = tasks.map(item => item._id);
  const sortedDeps = depList.sort((a, b) => listIds.indexOf(b) - listIds.indexOf(a));
  return sortedDeps[0];
}

// Dynamically get the row based on logic
function dynamicallyGetRow(task, visited) {
  // Example logic for assigning rows
  return visited.size + 1; // Adjust based on your requirements
}

// Adjust the schedule for backward dates
function adjustedDateBackward(visited, task) {
  // Adjust other tasks if necessary based on backward scheduling
  // This function should ensure no overlap or conflicts in scheduling
}


function sanitizeTasks(tasks) {
  // Handle null, undefined, or empty array cases
  if (!tasks || tasks.length === 0) {
    return [];
  }

  return tasks.map(task => ({
    _id: task._id,
    name_text: task.name_text,
    duration: task.duration,
    deps: (task.dependencies_list_custom_dependency || [])
      .filter(dep =>
        dep &&
        dep.type === 'successor' &&
        dep.is_dependent_into === false &&
        dep.data_type === 'deliverable'
      )
      .map(dep => dep.obj._id),
    is_parallel: task.is_parallel || false,
    priority_option: task.priority_option,
    start: undefined,
    end: undefined,
    cursor: undefined,
    row: undefined,
    col: undefined,
    visitedSucessorTasks: new Map()
  }));
}

function sanitizeTasksForFixed(tasks) {
  // Handle null, undefined, or empty array cases
  if (!tasks || tasks.length === 0) {
    return [];
  }

  return tasks.map(task => ({
    _id: task._id,
    name_text: task.name_text,
    duration: task.duration,
    deps: (task.dependencies_list_custom_dependency || [])
      .filter(dep =>
        dep &&
        dep.type === 'successor' &&
        dep.is_dependent_into === false &&
        dep.data_type === 'deliverable'
      )
      .map(dep => dep.obj._id),
    is_parallel: task.is_parallel || false,
    priority_option: task.priority_option,
    start: task.start,
    end: task.end,
    cursor: undefined,
    row: undefined,
    col: undefined,
    visitedSucessorTasks: new Map()
  }));
}

function canOverlapWithOtherTasks(task, visited) {
  let lastOverlappingTask = null;

  // Iterate through the visited tasks
  for (const visitedTask of visited.values()) {
    // Check if the tasks overlap in their date ranges
    if (
      (task.start >= visitedTask.start && task.start <= visitedTask.end) || // Task starts during the visited task
      (task.end >= visitedTask.start && task.end <= visitedTask.end) || // Task ends during the visited task
      (visitedTask.start >= task.start && visitedTask.start <= task.end) // Visited task starts during the current task
    ) {
      lastOverlappingTask = visitedTask; // Update the last overlapping task
    }
  }

  // Return the last overlapping task or null if none found
  return lastOverlappingTask;
}

function addDays(date, days) { const newDate = new Date(date); newDate.setDate(date.getDate() - days); return newDate; }

function filterMapByRow(map, cursorValue) {
  return new Map(
    Array.from(map.entries()).filter(([key, value]) => value.row === cursorValue)
  );
}

function getLastEntry(map) {
  if (map.size === 0) return null; // Handle empty map case

  const lastEntry = [...map.entries()].pop(); // Get last key-value pair
  return { key: lastEntry[0], value: lastEntry[1] };
}

function getMinDateFromMap(tasksMap) {
  if (!(tasksMap instanceof Map) || tasksMap.size === 0) {
      return null; // Return null for invalid or empty input
  }

  let minDate = null;

  // Iterate through Map values
  for (const task of tasksMap.values()) {
      // Check start date if it exists
      const startDate = task.start instanceof Date ? task.start : new Date(task.start);
      if (!isNaN(startDate)) {
          if (minDate === null || startDate < minDate) {
              minDate = startDate;
          }
      }

      // Check end date if it exists
      const endDate = task.end instanceof Date ? task.end : new Date(task.end);
      if (!isNaN(endDate)) {
          if (minDate === null || endDate < minDate) {
              minDate = endDate;
          }
      }
  }

  return minDate;
}
