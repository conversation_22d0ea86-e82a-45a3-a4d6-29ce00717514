const Timeline = (function() {
    // Constants and global variables
    const deliverableUrl = '_*_deliverableUrl_*_';
    const dependencyUrl = '_*_dependencyUrl_*_';
    const updateActivityEndpoint = '_*_updateActivityEndpoint_*_';
    const updateDeliverableEndpoint = '_*_updateDeliverableEndpoint_*_';
    const activityUrl = '_*_activityUrl_*_';
    const allactivityURL = '_*_allactivityUrl_*_';
    const userUrl = '_*_userUrl_*_';

    const day = 24 * 36e5; // milliseconds in a day
    const today = Math.floor(Date.now() / day) * day; // current day in milliseconds
    let detailChart;
    const image_assets = {
        avatar1_icon: 'https://69906f258885a4f4ebf7e70d3efa75db.cdn.bubble.io/f1721643639754x508379335165734850/avatar_1.svg',
        avatar2_icon: 'https://69906f258885a4f4ebf7e70d3efa75db.cdn.bubble.io/f1721643644272x357221151790066300/avatar_2.svg',
        calendar_icon: 'https://69906f258885a4f4ebf7e70d3efa75db.cdn.bubble.io/f1721643522055x927944309165966200/dependent_out.svg',
        dependent_diamond_in_icon: 'https://69906f258885a4f4ebf7e70d3efa75db.cdn.bubble.io/f1721719449243x273223181152114620/dependent_ext.svg'
    };
    let allowChartUpdate = true;

    // Highcharts submodule
    const HC = (function() {
        const config = (function() {
            const options = {
                chart: {
                    plotBackgroundColor: '#FFFFFF',
                    plotBorderColor: '#787680',
                    plotBorderWidth: 1,
                    height: 338,
                    id: "timeX",
                    scrollablePlotArea: {
                        minHeight: 250,
                    },
                    events: {
                        load: function () {
                            this.plotBorder.attr({ rx: 18, ry: 18 });
                        },
                        render: function () {
                            if (allowChartUpdate) {
                                allowChartUpdate = false;
                                this.update({
                                    chart: {
                                        scrollablePlotArea: {
                                            minHeight: this.seriesGroup.getBBox(true).height + this.plotTop + 100
                                        }
                                    }
                                });
                                allowChartUpdate = true;
                            }
                        }
                    },
                    backgroundColor: '#FAF9FB',
                    style: { fontSize: 12 },
                    marginRight: 20,
                    spacingRight: 10
                },
                scrollbar: {
                    enabled: true,
                    trackBackgroundColor: '#f0e6ff',
                    barBackgroundColor: '#6b4ea1',
                    trackBorderColor: '#f0e6ff'
                },
                rangeSelector: { enabled: true, selected: 0 },
                exporting: { enabled: false },
                plotOptions: {
                    series: {
                        borderRadius: '50%',
                        borderWidth: 0.5,
                        borderColor: '#000',
                        connectors: {
                            dashStyle: 'Solid',
                            lineColor: '#3E5F90',
                            lineWidth: 2,
                            radius: 5,
                            startMarker: { enabled: false }
                        },
                        groupPadding: .14,
                        dataLabels: [
                            {
                                enabled: true,
                                align: 'left',
                                crop: false,
                                allowOverlap: true,
                                useHTML: true,
                                formatter: function () {
                                    let dependency_icon = '', dependency_modal = '', is_external_dep_present, dependency_css_string;
                                    is_external_dep_present = !this.point.is_type_active_period && this.point.dependencies && this.point.dependencies.length > 0 && this.point.task_type == 'deliverable';
                                    const is_type_active_period = this.point.is_type_active_period;
                                    const authorisers = this.point.assignees.authorisers || [];
                                    const contributors = this.point.assignees.contributors || [];
                                    const authorisers_counts = authorisers.length > 1 ? `<span class='custom-icon'>${authorisers.length - 1}+</span>` : ``;
                                    const contributors_count = contributors.length > 1 ? `<span class='custom-icon'>${contributors.length - 1}+</span>` : ``;
                                    const width = this.shapeArgs.width - 15;
                                    const height = this.shapeArgs.height;
                                    const authorisers_icon = authorisers.length > 0 ? `<img src="${authorisers[0].icon}" title="${authorisers[0].name}" class="">` : '';
                                    const contributors_icon = contributors.length > 0 ? `<img src="${contributors[0].icon}" title="${contributors[0].name}" class="">` : '';
                                    const priority = this.priority ? this.priority : '';
                                    const eventElement = `
                                        <div class="commet-trail-container" style="max-width: ${width}px; height: ${height}px">
                                            <div class="item">${this.point.name}</div>
                                            <div class="commet-middle">
                                                <div class="commet-icon-container">${authorisers_icon}${authorisers_counts}</div>
                                                <div class="commet-icon-container">${contributors_icon}${contributors_count}</div>
                                            </div>
                                            <div class="commet-last-child">${priority}</div>
                                        </div>`;
                                    const infoBar = this.point.is_type_active_period ? `<span class='a_name'>${this.point.name}</span>` : eventElement;

                                    if (is_external_dep_present) {
                                        dependency_icon = display_icon(this.point).left_icon;
                                        dependency_css_string = 'dependency';
                                        dependency_modal = dependency_modal_element(this.point);
                                    }

                                    return `
                                        <div class="pointer-first-part ${dependency_css_string} ${pointerCursor(!this.point.is_type_active_period)}">
                                            ${dependency_icon}${dependency_modal}${infoBar}
                                        </div>`;
                                },
                                padding: 10,
                                style: { fontWeight: 'bold', textOutline: 'none', height: 60 }
                            },
                            {
                                enabled: true,
                                align: 'center',
                                useHTML: true,
                                formatter: function () {
                                    const is_type_active_period = this.point.is_type_active_period;
                                    const authorisers = this.point.assignees.authorisers || [];
                                    const contributors = this.point.assignees.contributors || [];
                                    const authorisers_counts = authorisers.length > 1 ? `<span class='custom-icon'>${authorisers.length - 1}+</span>` : ``;
                                    const contributors_count = contributors.length > 1 ? `<span class='custom-icon'>${contributors.length - 1}+</span>` : ``;
                                    const authorisers_icon = authorisers.length > 0 ? `<img src="${authorisers[0].icon}" title="${authorisers[0].name}" class="custom-icon">` : '';
                                    const contributors_icon = contributors.length > 0 ? `<img src="${contributors[0].icon}" title="${contributors[0].name}" class="custom-icon">` : '';
                                    const contributor_div = !is_type_active_period ? `
                                        <div class="contributor flex-container ${pointerCursor(!this.point.is_type_active_period)}">
                                            <div class="flex-child"><div class="custom-icons">${authorisers_icon}${authorisers_counts}</div></div>
                                            <div class="divider"></div>
                                            <div class="flex-child"><div class="custom-icons">${contributors_icon}${contributors_count}</div></div>
                                        </div>` : '';
                                    return contributor_div;
                                },
                                padding: 10,
                                style: { fontWeight: 'bold', textOutline: 'none' }
                            },
                            {
                                enabled: true,
                                align: 'right',
                                formatter: function () {
                                    let dependency_icon = '', dependency_modal = '', is_external_dep_present, dependency_css_string;
                                    is_external_dep_present = !this.point.is_type_active_period && this.point.dependencies && this.point.dependencies.length > 0;
                                    if (is_external_dep_present) {
                                        dependency_icon = display_icon(this.point).right_icon;
                                        dependency_css_string = 'dependency';
                                    }
                                    if (is_external_dep_present && has_external_dependent(this.point)) {
                                        dependency_modal = dependency_modal_element(this.point);
                                    }
                                    return `<div class="pointer-third-part ${dependency_css_string}">${dependency_icon}${dependency_modal}</div>`;
                                },
                                useHTML: true,
                                padding: 10,
                                style: { fontWeight: 'normal', textOutline: 'none', opacity: 0.8 }
                            }
                        ],
                        point: {
                            events: {
                                click: function (e) {
                                    if (this.task_type == 'activity') {
                                        Timeline.HC.Renderer.renderSubEvents(this.series.chart, this.name, this.deliverables);
                                    }
                                    if (this.task_type == 'deliverable') {
                                        const cloneObj = createTsNewObject(this),
                                            deliverabeTimeObj = JSON.stringify(cloneObj);
                                        bubble_fn_showDeliverableDetails({ value: this.id, output1: deliverabeTimeObj });
                                    }
                                }
                            }
                        }
                    }
                },
                series: [],
                tooltip: {
                    pointFormat: '<span style="font-weight: bold">{point.name}</span><br>{point.start:%e %b}{#unless point.milestone} → {point.end:%e %b}{/unless}<br>',
                    useHTML: true,
                    backgroundColor: '#ebeef3',
                    borderWidth: 0,
                    shadow: '0px 3px 3px red',
                    style: { zIndex: 9000 },
                    className: 'activity_deliverables-tooltip',
                    enabled: false,
                    formatter: function () {
                        const deliverable1 = deliverable_count(100);
                        const deliverable2 = deliverable_count(100);
                        return `
                            <div class="activity_deliverables-tooltip container">
                                <div class="full-screen"><img src="./img/Subtract.svg" alt="expand view"></div>
                                <div class="progress-bar-container">${deliverable1}${deliverable2}</div>
                                <div class="group-buttons"><button>Show more</button><button>Close </button></div>
                            </div>`;
                    }
                },
                title: { text: '' },
                xAxis: [{
                    currentDateIndicator: {
                        color: '#ED2224',
                        dashStyle: 'Shortdot',
                        width: 3,
                        label: { format: '' }
                    },
                    dateTimeLabelFormats: { day: '%e<br><span style="opacity: 0.5; font-size: 0.7em">%a</span>' },
                    grid: { borderWidth: 0 },
                    gridLineWidth: 2,
                    min: today - 7 * day,
                    max: (today - 7 * day) + 30 * day,
                    custom: { today, weekendPlotBands: true },
                    gridLineColor: '#FDF7FF',
                    labels: { padding: 60, style: { fontSize: '12px', fontFamily: 'Lato' } }
                }],
                yAxis: { type: 'category', staticScale: 40, visible: false, max: 5 },
                accessibility: {
                    enabled: false,
                    keyboardNavigation: { seriesNavigation: { mode: 'serialize' } },
                    point: {
                        descriptionFormatter: function (point) {
                            const completedValue = point.completed ? point.completed.amount || point.completed : null;
                            const completed = completedValue ? ' Task ' + Math.round(completedValue * 1000) / 10 + '% completed.' : '';
                            const dependency = point.dependency && point.series.chart.get(point.dependency).name;
                            const dependsOn = dependency ? ' Depends on ' + dependency + '.' : '';
                            return Highcharts.format(
                                point.milestone ?
                                    '{point.yCategory}. Milestone at {point.x:%Y-%m-%d}. Owner: {point.owner}.{dependsOn}' :
                                    '{point.yCategory}.{completed} Start {point.x:%Y-%m-%d}, end {point.x2:%Y-%m-%d}. Owner: {point.owner}.{dependsOn}',
                                { point, completed, dependsOn }
                            );
                        }
                    }
                },
                lang: {
                    accessibility: {
                        axis: { xAxisDescriptionPlural: 'The chart has a two-part X axis showing time in both week numbers and days.' }
                    }
                }
            };

            function setUpDeliverableOption(timeSeries, config) {
                const min = findEarliestTimeDeliverables(timeSeries);
                const max = min + 30 * 24 * 60 * 60 * 1000;
                const padding = 2 * 24 * 3600 * 1000;
                const newOptions = $.extend(true, {}, options);

                if (Array.isArray(newOptions.xAxis)) {
                    newOptions.xAxis = [...newOptions.xAxis];
                    newOptions.xAxis[0] = { ...newOptions.xAxis[0], min: min - padding };
                }

                if (newOptions.rangeSelector && config.rangeSelector !== undefined) {
                    newOptions.rangeSelector = { ...newOptions.rangeSelector, enabled: config.rangeSelector };
                }

                if (newOptions.chart.height && config.height) {
                    newOptions.chart.height = (config.height || 0) + 10;
                }

                if (newOptions.yAxis.max && config.max) {
                    newOptions.yAxis.max = config.max;
                }

                return newOptions;
            }

            function setUpChartOption(eventsConfig) {
                const extremes = findTimeExtremities(eventsConfig.events);
                const min = extremes.min;
                const max = extremes.max;
                const padding = 2 * 24 * 3600 * 1000;
                const newOptions = $.extend(true, {}, options);

                if (Array.isArray(newOptions.xAxis)) {
                    newOptions.xAxis = [...newOptions.xAxis];
                    newOptions.xAxis[0] = { ...newOptions.xAxis[0], min: min - padding };
                }

                if (newOptions.rangeSelector && eventsConfig.rangeSelector !== undefined) {
                    newOptions.rangeSelector = { ...newOptions.rangeSelector, enabled: true };
                }

                if (newOptions.yAxis.max && eventsConfig.max) {
                    newOptions.yAxis.max = eventsConfig.max;
                }

                if (newOptions.chart.height && eventsConfig.height) {
                    newOptions.chart.height = eventsConfig.height;
                }
                
                return newOptions;
            }

            return {
                getOptions: () => $.extend(true, {}, options),
                setUpChartOption,
                setUpDeliverableOption
            };
        })();

        const Renderer = (function() {
            function initializeWeekendPlotBands() {
                Highcharts.addEvent(Highcharts.Axis, 'foundExtremes', e => {
                    if (e.target.options.custom && e.target.options.custom.weekendPlotBands) {
                        const axis = e.target;
                        const chart = axis.chart;
                        const day = 24 * 36e5;
                        const isWeekend = t => /[06]/.test(chart.time.dateFormat('%w', t));
                        const plotBands = [];
                        let inWeekend = false;

                        for (let x = Math.floor(axis.min / day) * day; x <= Math.ceil(axis.max / day) * day; x += day) {
                            const last = plotBands.at(-1);
                            if (isWeekend(x) && !inWeekend) {
                                plotBands.push({
                                    from: x,
                                    color: {
                                        pattern: {
                                            path: 'M 0 10 L 10 0 M -1 1 L 1 -1 M 9 11 L 11 9',
                                            width: 10,
                                            height: 10,
                                            color: '#FDF7FF',
                                            backgroundColor: 'rgba(253, 247, 255, 1)'
                                        }
                                    }
                                });
                                inWeekend = true;
                            }
                            if (!isWeekend(x) && inWeekend && last) {
                                last.to = x;
                                inWeekend = false;
                            }
                        }
                        axis.options.plotBands = plotBands;
                    }
                });
            }

            document.addEventListener('DOMContentLoaded', initializeWeekendPlotBands);

            function renderTimeline(newDataSeries, chartID) {
                if (document.getElementById(chartID)) {
                    const parentElement = $(`#${chartID}`).parent();
                    const calculatedHeight = parentElement.hasClass('defaultMode') ? parentElement.outerHeight() - 60 : parentElement.outerHeight();
                    const newOptions = config.setUpChartOption({ events: newDataSeries, height: calculatedHeight, max: 5 });
                    newOptions.series = newDataSeries;
                    Highcharts.ganttChart(chartID, newOptions, function (chart) {
                        initializeAuxiliaryTimelineFeatures(chart);
                        adjustEventDates(newDataSeries, chartID);
                    });
                    showTimelineLoader(chartID);
                    const currentTimeWithMonth = new Date().toLocaleString('en-GB', { hour: '2-digit', minute: '2-digit', second: '2-digit' }).replace(',', '');
                    console.debug("renderTimeline called at:", currentTimeWithMonth);
                }
            }

            function renderTimelineWithOption(newDataSeries, chartID, configOptions) {
                if (document.getElementById(chartID)) {
                    configOptions.series = newDataSeries;
                    Highcharts.ganttChart(chartID, configOptions, function (chart) {
                        initializeAuxiliaryTimelineFeatures(chart);
                    });
                    showTimelineLoader(chartID);
                }
            }

            function renderSubEvents(chart, superEventName, subEvents) {
                const chartContainer = $(`#${chart.renderTo.id}`)[0].parentNode;
                const eventChart = $(`#${chart.renderTo.id}`);
                const subEventChart = $(chartContainer).find('#detailView');
                const toggleFullscreenButton = $(chartContainer).find('#toggle-fullscreen');
                const eventNavButton = $(chartContainer).find('#activity_overview');
                const subEventNavButton = $(chartContainer).find('#active_activity');

                if (!subEventChart.is(":visible") && subEvents.length > 0) {
                    const parentElement = $(`#${chart.renderTo.id}`).parent();
                    const calculatedHeight = parentElement.hasClass('defaultMode') ? parentElement.outerHeight() - 80 : parentElement.outerHeight();
                    const max = chart.renderTo.id === 'fullChart' ? 20 : 5;
                    const newOpts = config.setUpDeliverableOption(subEvents, { height: calculatedHeight, rangeSelector: true, max });
                    const chartData = { ...newOpts, series: [{ name: 'Deliverables Timeline', data: subEvents }] };

                    Highcharts.ganttChart(subEventChart[0], chartData, function () {
                        const firstEvent = subEvents.at(-1);
                        const FOUR_DAYS_IN_MS = 3 * 24 * 60 * 60 * 1000;
                        const TWENTY_SEVEN_DAYS_IN_MS = 27 * 24 * 60 * 60 * 1000;
                        const adjustedStart = firstEvent.start - FOUR_DAYS_IN_MS;
                        const adjustedEnd = firstEvent.end + TWENTY_SEVEN_DAYS_IN_MS;
                        const chart = (Highcharts.charts || []).filter(c => c).find(c => c.renderTo === subEventChart[0]);
                        chart.xAxis[0].setExtremes(adjustedStart, adjustedEnd);
                    });

                    subEventNavButton.text(superEventName).removeClass('hidden');
                    subEventChart.removeClass('hidden');
                    eventChart.addClass('hidden');
                }
            }

            async function renderAllActivityTimeline() {
                const activities = await fetchActiveActivitiesData();
                const timeline = Generator.extractTimeSeriesFromFetchedActivitiesJSON(activities);
                window.localStorage.setItem("timelineSeries", JSON.stringify(timeline));
                renderTimeline(timeline, 'dashboard');
                renderTimeline(timeline, 'cna');
            }

            function renderCurrentActivityTimeline(activity_id) {
                // Placeholder for future implementation
            }

            function renderComponentTimeline(componentID) {
                const activities = JSON.parse(localStorage.fetchedActivities || '[]');
                const activeTimeSeries = Generator.extractTimeSeriesFromFetchedActivitiesJSON(activities);
                renderTimeline(activeTimeSeries, componentID);
            }

            async function renderCompareActivities(activityId, fullScreen = null) {
                showTimelineLoader('cna', true);
                const mergedActivities = await mergeCurrentActivityAndActiveActivities(activityId);
                const currentTimeWithMonth = new Date().toLocaleString('en-GB', { hour: '2-digit', minute: '2-digit', second: '2-digit' }).replace(',', '');
                const currentEventId = { id: activityId, type: 'activity' };
                const newTimeSeries = Generator.extractTimeSeriesFromFetchedActivitiesJSON(mergedActivities, currentEventId);
                renderTimeline(newTimeSeries, 'cna');
            }

            async function renderDeliverablesTimeline(a_id, d_id = null, chartElementID) {
                showTimelineLoader(chartElementID, true);
                const currentEventId = d_id ? { id: d_id, type: 'deliverable' } : null;
                try {
                    const a_obj = await Generator.generateActivityTimeseries(a_id, currentEventId);
                    const calculatedHeight = $(`#${chartElementID}`).hasClass('defaultView') ? $(`#${chartElementID}`).height() - 66 : $(`#${chartElementID}`).parent().height();
                    const newOpts = config.setUpDeliverableOption(a_obj.activityTimeline.deliverables, { height: calculatedHeight, rangeSelector: false });
                    const chartData = { ...newOpts, series: [{ name: 'Deliverables Timeline', data: a_obj.activityTimeline.deliverables }] };
                    Highcharts.ganttChart(chartElementID, chartData);
                } catch (error) {
                    console.error("Error rendering activity timeline:", error);
                } finally {
                    showTimelineLoader(chartElementID);
                }
            }

            async function renderSetupDeliverablesTimeline(a_id, d_id = null, chartElementID) {
                showTimelineLoader(chartElementID, true);
                const currentEventId = d_id ? { id: d_id, type: 'deliverable' } : null;
                try {
                    const a_obj = await Generator.generateActivityTimeseries(a_id, currentEventId);
                    const parentElement = $(`#${chartElementID}`).parent();
                    const calculatedHeight = parentElement.hasClass('defaultMode') ? parentElement.outerHeight() - 60 : parentElement.outerHeight();
                    const newOpts = config.setUpDeliverableOption(a_obj.activityTimeline.deliverables, { height: calculatedHeight, rangeSelector: true, max: 5 });
                    const chartData = { ...newOpts, series: [{ name: 'Deliverables Timeline', data: a_obj.activityTimeline.deliverables }] };
                    Highcharts.ganttChart(chartElementID, chartData, function (chart) {
                        initializeAuxiliaryTimelineFeatures(chart);
                    });
                    adjustEventDates(a_obj.activityTimeline.deliverables, chartElementID);
                } catch (error) {
                    console.error("Error rendering activity timeline:", error);
                } finally {
                    showTimelineLoader(chartElementID);
                }
            }

            async function renderSingleDeliverableTimeline(id, data) {
                const chartID = `deliverableSideSheet${id}`;
                const yAxis = 2;
                const parentElement = $(`#${chartID}`).parent();
                const calculatedHeight = parentElement.outerHeight();
                showTimelineLoader(chartID, true);

                const deliverableTsData = data ? JSON.parse(data) : await Generator.generateDeliverableTimeseries(id);
                const { start, end, name } = deliverableTsData;
                const adjustDate = (date, offset) => new Date(new Date(date).getTime() + offset).toISOString();
                const extremeStart = adjustDate(start, -86400000);
                const extremeEnd = adjustDate(end, 86400000);

                Object.assign(deliverableTsData, { y: 1, borderWidth: 3, borderColor: '#3E5F90' });
                const newOpts = config.setUpDeliverableOption([deliverableTsData], { height: calculatedHeight, max: yAxis });
                newOpts.rangeSelector.enabled = false;
                const chartData = { ...newOpts, series: [{ name, data: [deliverableTsData] }] };

                if (document.getElementById(chartID)) {
                    Highcharts.ganttChart(chartID, chartData, function (chart) {
                        chart.xAxis[0].setExtremes(extremeStart, extremeEnd);
                    });
                    showTimelineLoader(chartID, false);
                }
            }

            function renderErrorOnTimeline(messages = []) {
                const cnaTimeline = $('.timeline #cna');
                const dTimeline = $('.timeline #deliverable');
                if (cnaTimeline.length === 0 && dTimeline.length === 0) {
                    console.error('Both timelines are missing in the DOM');
                    return;
                }
                if (messages.length === 0) messages = ['Error: Something went wrong.', 'Please check the timeline.'];
                const messagesList = messages.map(message => `<li>${message}</li>`).join('');

                const insertMessages = (timelineElement) => {
                    if (timelineElement.length > 0) {
                        const parentElement = timelineElement.parent();
                        parentElement.addClass('error');
                        let errorList = parentElement.find('ul.error_messages');
                        if (errorList.length === 0) {
                            errorList = $('<ul class="error_messages"></ul>');
                            parentElement.append(errorList);
                        }
                        errorList.html(messagesList);
                    }
                };
                insertMessages(cnaTimeline);
                insertMessages(dTimeline);
            }

            function clearErrorOnTimeline() {
                const cnaTimeline = $('.timeline #cna');
                const dTimeline = $('.timeline #deliverable');
                const clearMessages = (timelineElement) => {
                    if (timelineElement.length > 0) {
                        const parentElement = timelineElement.parent();
                        parentElement.removeClass('error');
                        parentElement.find('ul.error_message').remove();
                    }
                };
                clearMessages(cnaTimeline);
                clearMessages(dTimeline);
            }

            function showTimelineLoader(chartID, state = false) {
                if (state) {
                    $('#' + chartID).siblings('.loading-overlay').addClass('active');
                } else {
                    $('#' + chartID).siblings('.loading-overlay').removeClass('active');
                }
            }

            function initializeAuxiliaryTimelineFeatures(chart) {
                const chartContainer = chart.container.parentNode.parentNode;
                const eventChart = $(chartContainer).find(`#${chart.renderTo.id}`);
                const subEventChart = $(chartContainer).find('#detailView');
                const toggleFullscreenButton = $(chartContainer).find('#toggle-fullscreen');
                const hideFullscreenButton = $(chartContainer).find('#hide-fullscreen');
                const eventNavButton = $(chartContainer).find('#activity_overview');
                const subEventNavButton = $(chartContainer).find('#active_activity');

                toggleFullscreenButton.attr('data-chart-id', chart.renderTo.id);

                toggleFullscreenButton.on('click', function () {
                    toggleFullScreen(this);
                });

                hideFullscreenButton.on('click', function () {
                    hideFullScreen(this);
                });

                eventNavButton.on('click', function () {
                    backToSuperEvent(this, chartContainer);
                });

                async function toggleFullScreen(button) {
                    bubble_fn_fullTimelineModal(true);
                    const chartId = $(button).attr('data-chart-id');
                    const fullChartModal = 'fullChart';
                    const p = $('#fullChart').parent();
                    showTimelineLoader(fullChartModal, true);

                    const chartInstance = Highcharts.charts.filter(c => c !== undefined).find(c => c.renderTo.id === chartId);
                    const a = await fetchActiveActivitiesData();
                    const timeseries = Generator.extractTimeSeriesFromFetchedActivitiesJSON(a);
                    const height = p.outerHeight() - 70;
                    const configOptions = config.setUpChartOption({ events: timeseries, max: 20, height });
                    renderTimelineWithOption(timeseries, fullChartModal, configOptions);
                }

                function hideFullScreen() {
                    bubble_fn_fullTimelineModal(false);
                }

                function backToSuperEvent() {
                    if (subEventChart.is(":visible")) {
                        subEventNavButton.text('').addClass('hidden');
                        subEventChart.addClass('hidden');
                        eventChart.removeClass('hidden');
                    }
                }
            }

            function adjustEventDates(list, chartId) {
                if (!(Array.isArray(list) && list.length > 0)) return null;
                const start = chartId === 'deliverable' ? findCurrentSubEventStart(list) : findCurrentEventStart(list);
                if (!start) return null;

                const chartInstance = Highcharts.charts.filter(c => c !== undefined).find(c => c.renderTo.id === chartId);
                const FOUR_DAYS_IN_MS = 3 * 24 * 60 * 60 * 1000;
                const TWENTY_SEVEN_DAYS_IN_MS = 27 * 24 * 60 * 60 * 1000;
                const adjustedStart = start - FOUR_DAYS_IN_MS;
                const adjustedEnd = adjustedStart + TWENTY_SEVEN_DAYS_IN_MS;
                chartInstance.xAxis[0].setExtremes(adjustedStart, adjustedEnd);
            }

            return {
                renderTimeline,
                renderTimelineWithOption,
                renderSubEvents,
                renderAllActivityTimeline,
                renderCurrentActivityTimeline,
                renderComponentTimeline,
                renderCompareActivities,
                renderDeliverablesTimeline,
                renderSetupDeliverablesTimeline,
                renderSingleDeliverableTimeline,
                renderErrorOnTimeline,
                clearErrorOnTimeline,
                showTimelineLoader,
                initializeAuxiliaryTimelineFeatures,
                adjustEventDates
            };
        })();

        const Generator = (function() {
            function generateTimeSeries(activity, index, currentEventId) {
                const isCurrentEvent = (currentEventId && currentEventId.id === activity._id && currentEventId.type === 'activity');
                const borderWidth = isCurrentEvent ? 3 : 0.5;
                const borderColor = isCurrentEvent ? '#64558F' : '#3E5F90';
                const activityStyle = getItemStyle("green", 0.2);

                const activityTimeline = {
                    id: `Id${activity._id}`,
                    name: activity.name_text,
                    is_type_active_period: false,
                    start: dateToUTCTimestamp(activity.work_start_date),
                    end: dateToUTCTimestamp(activity.start_date_date),
                    completed: activityStyle.completed,
                    color: activityStyle.colour,
                    owner: activity.activity_creator_user,
                    dependency: getExternalDependencies(activity.dependencies),
                    is_starred: false,
                    assignees: deriveAssigns(activity, 'activity'),
                    status: activityStyle.status,
                    y: index,
                    has_dependant: activity.dependencies && activity.dependencies.length > 0,
                    zIndex: 1,
                    task_type: "activity",
                    className: "pointer-cursor",
                    deliverables: [],
                    dependencies: activity.dependencies,
                    borderWidth,
                    borderColor,
                    is_current_event: isCurrentEvent
                };

                activity.deliverables_list_custom_deliverable2.forEach((deliverable, idx, array) => {
                    const reversedIndex = array.length - 1 - idx;
                    const deliverableTimeline = generateDeliverableTimeline(deliverable, activity, reversedIndex, currentEventId);
                    activityTimeline.deliverables.push(deliverableTimeline);
                });

                const baseMilestone = {
                    is_type_active_period: true,
                    start: dateToUTCTimestamp(activity.start_date_date),
                    owner: activity.activity_creator_user,
                    assignees: [],
                    y: index,
                    borderWidth,
                    className: "custom-point-1",
                    borderColor,
                    zIndex: 1,
                    milestone: false
                };

                let launchMilestone;
                if (!activity.end_date_date) {
                    launchMilestone = {
                        ...baseMilestone,
                        name: "",
                        zIndex: 1,
                        milestone: true,
                        connectors: { dashStyle: 'dash', lineColor: '#C8C5D0', lineWidth: 2, radius: 5, startMarker: { enabled: false } },
                        dependency: activity._id
                    };
                } else {
                    launchMilestone = {
                        ...baseMilestone,
                        name: "LIVE",
                        end: dateToUTCTimestamp(activity.end_date_date),
                        completed: { amount: 1, fill: "#D5E3FF" },
                        milestone: false
                    };
                }

                return { activityTimeline, launchMilestone };
            }

            function generateDeliverableTimeline(deliverable, activity, index, currentEventId) {
                const deliverableStyle = getItemStyle("green", 0.2);
                const start = convertIsoToDate(deliverable.start);
                const end = convertIsoToDate(deliverable.end);
                const position = typeof deliverable?.position !== 'undefined' ? deliverable.position : index;
                const isCurrentEvent = (currentEventId && currentEventId.id === deliverable._id && currentEventId.type === 'deliverable');
                const DborderWidth = isCurrentEvent ? 3 : 0.5;

                const generateRandomColor = () => `#${Math.floor(Math.random() * 16777215).toString(16)}`;
                const priorityToLevelTxt = (priority) => {
                    const priorityMap = {
                        'Critical (P1)': 'P1', 'High (P2)': 'P2', 'Medium (P3)': 'P3', 'Low (P4)': 'P4', 'Negligible (P5)': 'P5'
                    };
                    return priorityMap[priority] || '';
                };

                return {
                    id: deliverable._id,
                    name: deliverable.name_text,
                    activity_id: activity?._id,
                    start: dateToUTCTimestamp(start),
                    end: dateToUTCTimestamp(end),
                    dependency: getInternalDependencies(deliverable.dependencies_list_custom_dependency),
                    completed: deliverableStyle.completed,
                    color: deliverableStyle.colour,
                    owner: activity?.activity_creator_user,
                    assignees: deriveAssigns(deliverable, 'deliverable'),
                    status: deliverableStyle.status,
                    has_dependant: deliverable.dependencies_list_custom_dependency.length > 0,
                    zIndex: 1,
                    task_type: "deliverable",
                    dependencies: deliverable.dependencies_list_custom_dependency,
                    y: index,
                    borderWidth: DborderWidth,
                    borderColor: '#64558F',
                    priority: priorityToLevelTxt(deliverable.priority_option),
                    is_current_event: isCurrentEvent,
                    connectors: { lineColor: generateRandomColor() }
                };
            }

            async function generateActivityTimeseries(activity_id, currentEventId) {
                const getA = await fetchActivityDataByID(activity_id);
                return generateTimeSeries(getA, 0, currentEventId);
            }

            async function generateDeliverableTimeseries(id) {
                const deliverable = await buildDeliverable(id);
                return generateDeliverableTimeline(deliverable);
            }

            function extractTimeSeriesFromFetchedActivitiesJSON(fetchActiveActivitiesDataResult, currentEvent = null) {
                const activities = fetchActiveActivitiesDataResult;
                const currentEventID = currentEvent;
                return activities.map((activity, index) => {
                    const { activityTimeline, launchMilestone } = generateTimeSeries(activity, index, currentEventID);
                    return {
                        name: `${activity.name_text + 1} ${index + 1}`,
                        id: activity._id,
                        data: [activityTimeline, launchMilestone]
                    };
                });
            }

            function generateNumberList(arr) {
                return Array.from({ length: arr.length }, (v, i) => i);
            }

            function generate_dependecies(t, i, tn) {
                return {
                    id: Math.random(10),
                    source_id: 'deliverable_1',
                    target_id: i,
                    type: t,
                    target_name: tn
                };
            }

            function deliverable_count(count) {
                return `
                    <div class="progress-item ${count}%" style="width: '${count}%';">
                        <div class="progress-bar">
                            <div class="progress">
                                <span class="progress-text">Deliverable 1</span>
                                <div class="contributor flex-container">
                                    <div class="flex-child">
                                        <div class="custom-icons">
                                            <img src="./img/avatar_1.svg" title="David" class="custom-icon">
                                            <span class="custom-icon"></span>
                                        </div>
                                    </div>
                                    <div class="divider"></div>
                                    <div class="flex-child">
                                        <div class="custom-icons">
                                            <img src="./img/avatar_1.svg" title="David" class="custom-icon">
                                            <span class="custom-icon"></span>
                                        </div>
                                    </div>
                                </div>
                                <span></span>
                            </div>
                        </div>
                    </div>`;
            }

            return {
                generateTimeSeries,
                generateDeliverableTimeline,
                generateActivityTimeseries,
                generateDeliverableTimeseries,
                extractTimeSeriesFromFetchedActivitiesJSON,
                generateNumberList,
                generate_dependecies,
                deliverable_count
            };
        })();

        return {
            config,
            Renderer,
            Generator
        };
    })();

    // Core Timeline functions
    async function fetchJson(url) {
        try {
            const response = await fetch(url);
            if (response.ok) return await response.json();
        } catch (error) {
            console.error(`Error fetching data from ${url}:`, error);
        }
        return null;
    }

    function fetchApiResponse(apiUrl, constraints = [], additional_sort_fields = [], limit = null, cursor = null, headers = { 'Content-Type': 'application/json' }) {
        const buildQueryString = params => Object.keys(params).map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`).join('&');
        const params = {};
        if (constraints?.length) params.constraints = JSON.stringify(constraints);
        if (additional_sort_fields?.length) params.additional_sort_fields = JSON.stringify(additional_sort_fields);
        if (limit !== null && typeof limit === "string") params.limit = limit;
        if (cursor !== null && typeof cursor === "number") params.cursor = cursor;
        const queryString = buildQueryString(params);
        const fullUrl = queryString ? `${apiUrl}?${queryString}` : apiUrl;
        return fetch(fullUrl, { method: 'GET', headers })
            .then(response => response)
            .catch(error => { console.error('Fetch Error:', error); throw error; });
    }

    async function fetchNameById(id, type) {
        const url = type === "deliverable" ? deliverableUrl : activityUrl;
        const data = await fetchJson(url.replace('[uid]', id));
        return data?.response || null;
    }

    async function fetchDependencies(dependenciesList) {
        const results = await Promise.all(
            (dependenciesList || []).map(async dependencyId => {
                const dependencyData = await fetchJson(dependencyUrl.replace('[uid]', dependencyId));
                if (dependencyData) {
                    const objectData = await (dependencyData.response.deliverable_custom_deliverable2
                        ? fetchNameById(dependencyData.response.deliverable_custom_deliverable2, 'deliverable')
                        : fetchNameById(dependencyData.response.activity_custom_activity1, 'activity'));
                    const event = await buildActivityObjectForDeliverable(dependencyData.response, objectData);
                    return {
                        _id: dependencyData.response._id,
                        source_id: dependencyData.response.source_id_text,
                        target_id: dependencyData.response.target_id_text,
                        type: dependencyData.response.type_text,
                        is_dependent_into: dependencyData.response.is_dependent_into_boolean,
                        data_type: dependencyData.response.deliverable_custom_deliverable2 ? 'deliverable' : 'activity',
                        obj: objectData,
                        activity: event
                    };
                }
                return null;
            })
        );
        return results.filter(result => result !== null);
    }

    async function buildActivityObjectForDeliverable(dependency, obj) {
        if (dependency.deliverable_custom_deliverable2 && dependency.is_dependent_into_boolean && obj.activity_custom_activity1) {
            return await fetchNameById(obj.activity_custom_activity1);
        }
    }

    async function fetchAssignments(assignments) {
        const results = await Promise.all(
            (assignments || []).map(async assignmentId => {
                const assignmentData = await fetchJson(assignmentUrl.replace('[uid]', assignmentId));
                if (assignmentData) {
                    return {
                        _id: assignmentData.response._id,
                        assigned_role: assignmentData.response.role_option_assigned_role,
                        status: assignmentData.response.status_option_activity_status,
                        user: assignmentData.response.user_user,
                        is_activity: assignmentData.response.is_activity_boolean,
                    };
                }
                return null;
            })
        );
        return results.filter(result => result !== null);
    }

    async function fetchDeliverablesList(deliverablesList) {
        return await Promise.all((deliverablesList || []).map(buildDeliverable));
    }

    async function buildDeliverable(deliverableId) {
        const deliverableData = await fetchJson(deliverableUrl.replace('[uid]', deliverableId));
        if (!deliverableData) return null;
        const dependenciesList = await fetchDependencies(deliverableData.response.dependencies_list_custom_dependency);
        return {
            _id: deliverableData.response._id,
            name_text: deliverableData.response.name_text,
            approval_time_number: deliverableData.response.approval_time_number,
            time_period_number: deliverableData.response.time_period_number,
            duration: (deliverableData.response.time_period_number || 0) + (deliverableData.response.approval_time_number || 0),
            user_contributors_list_custom_user_assignment: deliverableData.response.user_contributors_list_custom_user_assignment || [],
            contributors: deliverableData.response.user_contributors_list_custom_user_assignment || [],
            authorisers: deliverableData.response.user_authorisers_list_custom_user_assignment || [],
            position: deliverableData.response.position_number || 0,
            dependencies_list_custom_dependency: dependenciesList,
            is_parallel: deliverableData.response.is_parallel__boolean || false,
            is_fixed: deliverableData.response.is_fixed_boolean || false,
            priority_option: deliverableData.response.priority_option_priority || undefined,
            start: deliverableData.response.start_date_date,
            end: deliverableData.response.end_date_date
        };
    }

    async function fetchActiveActivitiesData() {
        const constraints = [{ "key": "status_option_activity_status", "constraint_type": "equals", "value": "Active" }];
        const additional_sort_fields = [{ sort_field: "Created Date", descending: false }];
        const limit = '20';
        const activitiesResponse = await fetchApiResponse(allactivityURL, constraints, additional_sort_fields, limit);
        if (!activitiesResponse.ok) throw new Error('Failed to fetch activities');
        const activitiesData = await activitiesResponse.json();
        const activities = activitiesData.response.results;
        const cleanedActivityJsonList = await Promise.all(
            activities.map(async activity => {
                try {
                    const deliverablesList = await fetchDeliverablesList(activity.deliverables_list_custom_deliverable2);
                    const dependenciesList = await fetchDependencies(activity.dependencies_list_custom_dependency);
                    const actors = extractAuthorisersAndContributors(deliverablesList);
                    return {
                        _id: activity._id,
                        name_text: activity.name_text,
                        approval_time_number: activity.approval_time_number,
                        start_date_date: new Date(activity.start_date_date),
                        end_date_date: activity.end_date_date ? new Date(activity.end_date_date) : null,
                        activity_creator_user: activity['Created By'],
                        deliverables_list_custom_deliverable2: deliverablesList.filter(Boolean),
                        dependencies: dependenciesList.filter(Boolean),
                        work_start_date: activity.workstartdate_date,
                        work_end_date: activity.workenddate_date,
                        authorisers: activity.user_authorisers_list_custom_user_assignment,
                        deliverable_authorisers: actors.authorisers,
                        deliverable_contributors: actors.contributors
                    };
                } catch (error) {
                    console.error("Error processing activity: ", activity._id, error);
                    return null;
                }
            })
        );
        window.localStorage.setItem("fetchedActivities", JSON.stringify(cleanedActivityJsonList.filter(Boolean)));
        return cleanedActivityJsonList.filter(Boolean);
    }

    async function fetchActivityDataByID(id) {
        try {
            const activityData = await fetchJson(activityUrl.replace('[uid]', id));
            if (!activityData || !activityData.response) throw new Error('Failed to fetch activity data');
            const activity = activityData.response;
            const deliverablesList = await fetchDeliverablesList(activity.deliverables_list_custom_deliverable2);
            const dependenciesList = await fetchDependencies(activity.dependencies_list_custom_dependency);
            const actors = extractAuthorisersAndContributors(deliverablesList);
            const result = {
                _id: activity._id,
                name_text: activity.name_text,
                approval_time_number: activity.approval_time_number,
                start_date_date: new Date(activity.start_date_date),
                end_date_date: activity.end_date_date ? new Date(activity.end_date_date) : null,
                activity_creator_user: activity['Created By'],
                deliverables_list_custom_deliverable2: deliverablesList.filter(Boolean),
                dependencies: dependenciesList.filter(Boolean),
                work_start_date: activity.workstartdate_date,
                work_end_date: activity.workenddate_date,
                authorisers: activity.user_authorisers_list_custom_user_assignment,
                deliverable_authorisers: actors.authorisers,
                deliverable_contributors: actors.contributors
            };
            localStorage.setItem('currentActivity', JSON.stringify(result));
            return result;
        } catch (error) {
            throw new Error(error.message);
        }
    }

    function extractAuthorisersAndContributors(list) {
        const result = { authorisers: [], contributors: [] };
        list.forEach(item => {
            if (Array.isArray(item.authorisers)) result.authorisers.push(...item.authorisers);
            if (Array.isArray(item.contributors)) result.contributors.push(...item.contributors);
        });
        result.authorisers = [...new Set(result.authorisers)];
        result.contributors = [...new Set(result.contributors)];
        return result;
    }

    function dependency_modal_element(point) {
        const dependencies_text = display_message(point);
        return `
            <div class="modal ${point.status}">
                <div class="inner-content">
                    <div>
                        <div class="text-bold">External dependency(ies):</div>
                        <div class="inline-flex">${dependencies_text}</div>
                    </div>
                </div>
            </div>`;
    }

    function contributor_div(assignees) {
        const assigner_counts = `<span class='custom-icon'></span>`;
        const assigneesHtml = assignees.map(assignee => `<img src="${assignee.icon}" title="${assignee.name}" class="custom-icon">`).join('');
        return `
            <div class="contributor flex-container">
                <div class="flex-child"><div class="custom-icons">${assigneesHtml}${assigner_counts}</div></div>
                <div class="divider"></div>
                <div class="flex-child"><div class="custom-icons">${assigneesHtml}${assigner_counts}</div></div>
            </div>`;
    }

    function pointerCursor(t) {
        return t ? 'pointer-cursor' : '';
    }

    function display_icon(obj) {
        let left_icon = '', right_icon = '';
        const renderButton = (s, i, t) => `<button class='fab ${s} ${t}'><img src=${i} title="" class="custom-icon"></button>`;
        for (let dep of obj.dependencies) {
            if (dep.is_dependent_into) {
                left_icon = renderButton(obj.status, image_assets.dependent_diamond_in_icon, 'ext');
            }
        }
        return { left_icon, right_icon };
    }

    function display_message(obj) {
        return obj.dependencies
            .filter(dep => dep.is_dependent_into)
            .map(dep => `${(dep.activity ? dep.activity.name_text + ' : ' : '') + dep.obj.name_text || ''}`)
            .join(', ');
    }

    function has_external_dependent(obj) {
        return obj.task_type === 'deliverable' && obj.dependencies.some(dep => dep.target_id !== obj.activity_id);
    }

    function getTimelineElements(chartElement) {
        const timelineSection = chartElement.container.parentElement.parentElement;
        return {
            timelineSection,
            toggleDependencies: timelineSection.querySelector('#toggle-fullscreen'),
            activeActivityLink: timelineSection.querySelector('#active_activity'),
            overviewLink: timelineSection.querySelector('#activity_overview')
        };
    }

    function dateToUTCTimestamp(dateString) {
        return new Date(dateString).getTime();
    }

    function getFullName(user) {
        const firstName = user.first_name_text || '';
        const lastName = user.last_name_text || '';
        return `${firstName} ${lastName}`.trim();
    }

    function getItemStyle(status, progress) {
        const colors = { green: "#D5F6E8", amber: "#FFE5B4", red: "#FFCCCB" };
        const color = colors[status] || colors.green;
        const amount = Math.min(Math.max(progress, 0), 1);
        function hexToRgb(hex) {
            const bigint = parseInt(hex.slice(1), 16);
            return `${(bigint >> 16) & 255}, ${(bigint >> 8) & 255}, ${bigint & 255}`;
        }
        return {
            colour: {
                linearGradient: { x1: 0, x2: 1, y1: 0, y2: 0, id: `highcharts-${Math.random().toString(36).substr(2, 9)}` },
                stops: [[0, color], [amount, color], [amount, `rgba(${hexToRgb(color)}, 0.5)`], [0.5, `rgba(${hexToRgb(color)}, 0.5)`], [0.5, "#fff"], [1, "#fff"]]
            },
            completed: { amount, fill: color },
            status
        };
    }

    function convertToDate(dateString) {
        const [day, month, year] = dateString.split('/');
        return new Date(`${year}-${month}-${day}`);
    }

    function convertIsoToDate(isoString) {
        const date = new Date(isoString);
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        return convertToDate(`${day}/${month}/${year}`);
    }

    function mapDependencies(dependencies) {
        return dependencies.map(dep => ({
            id: dep._id,
            source_id: dep.source_id,
            target_id: dep.deliverable || dep.activity,
            target_date: dep.obj,
            type: dep.is_dependent_into ? "dependent" : "independent"
        }));
    }

    function convertToJSformat(date) {
        if (typeof date === 'string') {
            const [day, month, year] = date.split('/');
            const fullYear = year.length === 2 ? `20${year}` : year;
            return `${month}/${day}/${fullYear}`;
        }
        return date;
    }

    async function mergeCurrentActivityAndActiveActivities(id) {
        const currentActivity = await fetchActivityDataByID(id);
        const fetchedActivities = await fetchActiveActivitiesData() || [];
        const updatedfetchedActivities = fetchedActivities.some(item => item._id === currentActivity._id)
            ? fetchedActivities.map(item => item._id === currentActivity._id ? currentActivity : item)
            : [...fetchedActivities, currentActivity];
        localStorage.setItem('CnaMergeActivities', JSON.stringify(updatedfetchedActivities));
        return updatedfetchedActivities;
    }

    async function processFeed(id, type) {
        try {
            !type ? HC.Renderer.showTimelineLoader('cna', true) : HC.Renderer.showTimelineLoader('deliverable', true);
            const activityfeed = await fetchActivityDataByID(id);
            const scheduler = new ReverseSchedulingAlgorithm();
            const schedule = scheduler.execute(activityfeed);
            if (schedule.success) {
                HC.Renderer.clearErrorOnTimeline();
                const updatedSchedule = await updateSchedule(id, schedule.schedules);
                if (updatedSchedule) {
                    if (type) {
                        HC.Renderer.renderSetupDeliverablesTimeline(id, type, 'deliverable');
                    } else {
                        HC.Renderer.renderCompareActivities(id);
                    }
                }
            } else {
                HC.Renderer.renderErrorOnTimeline([schedule.error]);
            }
        } catch (error) {
            HC.Renderer.renderErrorOnTimeline();
        }
    }

    async function updateSchedule(activity_id, deliverables) {
        const postRequest = async (url, data) => {
            try {
                const response = await fetch(url, {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify(data)
                });
                return response.ok;
            } catch (error) {
                console.error(`Failed to update data at ${url}:`, error);
                return false;
            }
        };
        const activityTimes = getDateRange(deliverables);
        const updateActivityData = { id: activity_id, workStartDate: activityTimes.start, workEndDate: activityTimes.end };
        const activityUpdated = await postRequest(updateActivityEndpoint, updateActivityData);
        if (!activityUpdated) {
            console.error(`Failed to update activity with ID ${activity_id}`);
            return false;
        }
        await Promise.all(deliverables.map(async task => {
            const updateDeliverableData = { id: task._id, start_date: task.start, end_date: task.end, position: task.position };
            const deliverableUpdated = await postRequest(updateDeliverableEndpoint, updateDeliverableData);
            if (!deliverableUpdated) console.error(`Failed to update deliverable with ID ${task._id}`);
        }));
        console.debug("Schedule updates completed successfully");
        return true;
    }

    function deriveTargetDeps(inputList, eventId) {
        if (!inputList) return { target_ids: [], source_ids: [] };
        const target_ids = inputList.filter(item => item.type === 'successor').map(item => item.target_id).filter(id => id && id !== eventId);
        const source_ids = inputList.filter(item => item.type === 'successor').map(item => item.source_id).filter(id => id && id !== eventId);
        return { target_ids, source_ids };
    }

    function deriveAssigns(obj, type) {
        const unique = array => [...new Set(array)];
        const formatAssignments = ids => ids.map(id => ({ name: id, icon: image_assets.avatar1_icon }));
        if (type === 'activity') {
            const authorisers = unique([...(obj.authorisers || []), ...(obj.deliverable_authorisers || [])]);
            const contributors = unique(obj.deliverable_contributors || []);
            return { authorisers: formatAssignments(authorisers), contributors: formatAssignments(contributors) };
        } else if (type === 'deliverable') {
            const authorisers = unique(obj.authorisers || []);
            const contributors = unique(obj.contributors || []);
            return { authorisers: formatAssignments(authorisers), contributors: formatAssignments(contributors) };
        }
        return { authorisers: [], contributors: [] };
    }

    function calculateWorkdays(givenDate, totalDays, backwards = true) {
        let currentDate = new Date(givenDate);
        if (totalDays === 0) {
            if (currentDate.getDay() === 6) currentDate.setDate(currentDate.getDate() - 1);
            else if (currentDate.getDay() === 0) currentDate.setDate(currentDate.getDate() - 2);
            return currentDate;
        }
        while (totalDays > 0) {
            currentDate.setDate(currentDate.getDate() + (backwards ? -1 : 1));
            if (currentDate.getDay() !== 0 && currentDate.getDay() !== 6) totalDays -= 1;
        }
        return currentDate;
    }

    function deriveRelated(dependencies, siblings) {
        const deliverableIds = (siblings || []).map(d => d._id);
        return (dependencies || []).filter(dep => deliverableIds.includes(dep.target_id) && !dep.is_dependent_into).map(dep => dep.target_id);
    }

    function getInternalDependencies(input) {
        return Array.isArray(input) ? input.filter(item => item.type === 'predecessor' && !item.is_dependent_into && item.data_type === "deliverable").map(item => item.source_id) : [];
    }

    function getExternalDependencies(input) {
        return Array.isArray(input) ? input.filter(item => item.type === 'predecessor' && item.is_dependent_into && item.data_type === "activity").map(item => `Id${item.source_id}`) : [];
    }

    const allResources = [];
    function deriveReservation(activity) {
        if (!activity) throw new Error("Activity object is required");
        const liveDate = convertToDate(activity.start_date_date);
        if (isNaN(liveDate.getTime())) throw new Error("Invalid live_date format");
        const approvalTimeInMilliseconds = (activity.approval_time_number || 0) * 24 * 60 * 60 * 1000;
        const activityLatestWorkEndDate = new Date(liveDate.getTime() - approvalTimeInMilliseconds);
        const randomString = Math.random().toString(36).substring(2, 10);

        function getPriorityValue(priorityOption) {
            const priorityMap = { 'p1': 100, 'p2': 80, 'p3': 60, 'p4': 40, 'p5': 20 };
            return priorityMap[priorityOption] || 50;
        }

        function getDeliverableResources(deliverable) {
            if (deliverable.is_parallel) {
                const rand = Math.random().toString(36).substring(2, 10);
                allResources.push(rand);
                return rand;
            }
            return randomString;
        }

        function getUniqueContributors(activityObject) {
            if (!activityObject.deliverables_list_custom_deliverable2?.length) return [];
            const uniqueContributors = allResources;
            uniqueContributors.push(randomString);
            return uniqueContributors.map(contributor => ({ id: contributor }));
        }

        const all_contributors = getUniqueContributors(activity);
        const deliverables = (activity.deliverables_list_custom_deliverable2 || []).map(deliverable => ({
            id: deliverable._id,
            duration: deliverable.duration,
            dependsOn: deriveRelated(deliverable.dependencies_list_custom_dependency, activity.deliverables_list_custom_deliverable2),
            priority: getPriorityValue(deliverable.priority_option),
            resources: getDeliverableResources(deliverable)
        }));

        return { completedDate: activityLatestWorkEndDate, deliverables, resources: all_contributors };
    }

    function buildScheduleObject(deliverables) {
        return schedule.tasks()
            .id(d => d.id)
            .duration(d => d.duration * 1440)
            .priority(d => d.priority)
            .minSchedule(d => d.duration * 1440)
            .dependsOn(d => d.dependsOn)
            .resources(d => d.resources || ['R1'])(deliverables);
    }

    function getTotalDuration(scheduleObj) {
        if (scheduleObj.success) return Math.ceil((scheduleObj.end - scheduleObj.start) / (1000 * 60 * 60 * 24));
        throw new Error("Auto scheduling failed!");
    }

    function findEarliestTimeDeliverables(events) {
        return events.reduce((earliest, event) => event.start < earliest ? event.start : earliest, Infinity);
    }

    function findTimeExtremities(timeSeries) {
        const allItems = timeSeries.flatMap(obj => obj.data);
        const startTimes = allItems.map(item => item.start).filter(Boolean);
        const endTimes = allItems.map(item => item.end).filter(Boolean);
        return { min: Math.min(...startTimes), max: Math.max(...endTimes) };
    }

    function getDateRange(items) {
        if (!items?.length) return { start: null, end: null };
        let earliestStart = null, latestEnd = null;
        items.forEach(item => {
            if (!item.start || !item.end) return;
            const startDate = new Date(item.start);
            const endDate = new Date(item.end);
            if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) return;
            if (earliestStart === null) {
                earliestStart = startDate;
                latestEnd = endDate;
                return;
            }
            if (startDate < earliestStart) earliestStart = startDate;
            if (endDate > latestEnd) latestEnd = endDate;
        });
        return { start: earliestStart, end: latestEnd };
    }

    function createTsNewObject(original) {
        return {
            id: original.object?.id,
            name: original.name,
            activity_id: original.activity_id,
            start: new Date(original.start).toISOString(),
            end: new Date(original.end).toISOString(),
            dependency: original.dependency,
            completed: original.completed,
            color: original.color,
            owner: original.owner,
            assignees: original.assignees,
            status: original.status,
            has_dependant: original.has_dependant,
            zIndex: original.zIndex,
            task_type: original.task_type,
            dependencies: original.dependencies,
            y: original.y,
            borderWidth: original.borderWidth,
            borderColor: original.borderColor,
            priority: original.priority
        };
    }

    function findCurrentEventStart(list) {
        const parent = list.find(obj => obj.data.some(event => event.is_current_event));
        if (parent) return parent.data.find(event => event.is_current_event).start;
        const lastEvent = list.at(-1);
        return lastEvent.data[0].start;
    }

    function findCurrentSubEventStart(list) {
        const currentEvent = list.find(event => event.is_current_event);
        return currentEvent ? currentEvent.start : null;
    }

    return {
        HC,
        fetchActiveActivitiesData,
        fetchActivityDataByID,
        processFeed,
        mergeCurrentActivityAndActiveActivities,
        deriveReservation,
        buildScheduleObject,
        getTotalDuration,
        updateSchedule
    };
})();