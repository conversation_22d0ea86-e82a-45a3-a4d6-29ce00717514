function(properties, context) {
    const asyncFunction = async () => {
        const activitiesRes = await BubbleDataJson.activities_to_json(properties.activities);

        if (activitiesRes.success) {
            const series = extractTimeSeriesFromFetchedActivitiesJSON(activitiesRes.object, properties.activity_id);
            renderTimeline(series, properties.type);
        } else {
            renderErrorOnTimeline([activitiesRes.error]);
        }
    }

    asyncFunction()
}
