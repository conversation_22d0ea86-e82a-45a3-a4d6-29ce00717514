function(properties, context) {
    const USER_API_TOKEN = context.keys["USER_API_TOKEN"];
    const UPDATE_SCHEDULE_ENDPOINT = context.keys["UPDATE_SCHEDULE_ENDPOINT"];

    // Helper to handle errors and hide loader
    const handleError = (errorMessage) => {
        renderErrorOnTimeline([errorMessage]);
        showTimelineLoader(properties.type, false);
    };

    // Helper to retry until condition is met
    const retryUntilCondition = (fetchFn, conditionFn, maxRetries = 10, retryDelay = 500) => {
        let attempts = 0;
        
        const attemptFetch = () => {
            fetchFn().then(result => {
                if (!result.success) {
                    handleError(result.error);
                    return;
                }
                if (conditionFn(result)) {
                    proceedWithRendering(result);
                } else if (attempts < maxRetries - 1) {
                    attempts++;n
                    setTimeout(attemptFetch, retryDelay);
                } else {
                    handleError("Backend workflow did not complete in time.");
                }
            });
        };
        attemptFetch();
    };

    // Helper to render timeline
    const proceedWithRendering = (updatedActivityRes) => {
        const currentEventID = { id: properties.deliverable_id, type: 'deliverable' };
        const series = generateTimelineForActivity(updatedActivityRes.object, currentEventID);
        clearErrorOnTimeline();
        renderTimelineToDeliverableChart(series, properties.type);
        showTimelineLoader(properties.type, false);
    };

    let activityRes; // Hoisted for access in retry logic
    const asyncFunction = async () => {
        showTimelineLoader(properties.type, true);

        activityRes = await BubbleDataJson.activity_to_json(properties.activity);
        if (!activityRes.success) {
            handleError(activityRes.error);
            return;
        }

        const scheduler = new ReverseSchedulingAlgorithm();
        const schedule = scheduler.execute(activityRes.object);
        if (!schedule.success) {
            handleError(schedule.error);
            return;
        }

        fetch(UPDATE_SCHEDULE_ENDPOINT, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${USER_API_TOKEN}`
            },
            body: JSON.stringify(schedule)
        })
            .then(response => {
                if (!response.ok) {
                    response.text().then(errorText => {
                        handleError(`HTTP error: ${response.status} - ${errorText || 'Unknown error'}`);
                    });
                    return;
                }
                retryUntilCondition(
                    () => BubbleDataJson.activity_to_json(properties.activity),
                    (res) => schedule.activity?.workStartDate.getTime() === res.object.work_start_date.getTime()
                );
            });
    };

    asyncFunction();
}