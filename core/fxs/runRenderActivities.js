function(properties, context) {
    const asyncFunction = async () => {
    const activitiesRes = await BubbleDataJson.activities_to_json(properties.activities);

    if (activitiesRes.success) {
        const series = extractTimeSeriesFromFetchedActivitiesJSON(activitiesRes.object);
        renderTimeline(series, properties.type);
    } else {
        renderErrorOnTimeline([activitiesRes.error]);
    }
   }

   asyncFunction()
}
