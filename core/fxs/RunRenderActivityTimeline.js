function(properties, context) {
    const asyncFunction = async () => {
    const activityRes = await BubbleDataJson.activity_to_json(properties.activity);

    if (activityRes.success) {
        const currentEventID = { id: properties.deliverable_id, type: 'deliverable' };
        const series = generateTimelineForActivity(activityRes.object, currentEventID);
        renderTimelineToDeliverableChart(series, properties.type);
        showTimelineLoader(properties.type, false);
    } else {
        renderErrorOnTimeline([activityRes.error]);
    }
   }

   asyncFunction()

}


const proceedWithRendering = (updatedActivityRes) => {
    const currentEventID = { id: properties.deliverable_id, type: 'deliverable' };
    const series = generateTimelineForActivity(updatedActivityRes.object, currentEventID);
    clearErrorOnTimeline();
    renderTimelineToDeliverableChart(series, properties.type);
    showTimelineLoader(properties.type, false);
};