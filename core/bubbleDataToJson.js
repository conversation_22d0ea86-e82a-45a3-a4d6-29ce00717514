const BubbleDataJsonModule = (function () {
    // Private helper functions with edge case handling
    async function unWrapActivities(inputList) {
        if (!inputList || typeof inputList.length !== 'function') return []; // Handle null/undefined/invalid inputList
        const listLength = await inputList.length();
        if (!listLength || listLength === 0) return []; // Handle empty list
        const items = await inputList.get(0, listLength);

        return await Promise.all(items.map(async (item) => {
            return await deepUnwrap(item) || null; // Return null if deepUnwrap fails
        })).then(results => results.filter(Boolean)); // Filter out nulls
    }

    async function deepUnwrap(activity) {
        if (!activity || typeof activity.get !== 'function') return null; // Handle null/undefined/invalid activity

        const deliverables = await unWrapDeliverable(await activity.get('deliverables_list_custom_deliverable2') || null) || [];
        const dependencies = await unWrapDependencies(await activity.get('dependencies_list_custom_dependency') || null) || [];
        const actors = extractAuthorisersAndContributors(deliverables.length ? deliverables : []);
        const creatorID = await unWrapCreatorID(await activity.get('Created By') || null) || null;
        const authorisers = await unWrapUserAssignees(await activity.get('user_authorisers_list_custom_user_assignment') || null) || [];

        return {
            _id: await activity.get('_id') || null,
            name_text: await activity.get('name_text') || '',
            approval_time_number: await activity.get('approval_time_number') || 0,
            start_date_date: await activity.get('start_date_date') || null,
            end_date_date: await activity.get('end_date_date') || null,
            activity_creator_user: creatorID,
            deliverables_list_custom_deliverable2: deliverables.filter(Boolean),
            dependencies: dependencies.filter(Boolean),
            work_start_date: await activity.get('workstartdate_date') || null,
            work_end_date: await activity.get('workenddate_date') || null,
            authorisers: authorisers,
            deliverable_authorisers: actors.authorisers || [],
            deliverable_contributors: actors.contributors || []
        };
    }

    async function unWrapDeliverable(inputList) {
        if (!inputList || typeof inputList.length !== 'function') return []; // Handle null/undefined/invalid inputList
        const listLength = await inputList.length();
        if (!listLength || listLength === 0) return []; // Handle empty list
        const items = await inputList.get(0, listLength);

        return await Promise.all(items.map(async (item) => {
            return await extractDeliverableData(item) || null; // Return null if extract fails
        })).then(results => results.filter(Boolean)); // Filter out nulls
    }

    async function extractDeliverableData(item) {
        if (!item || typeof item.get !== 'function') return null; // Handle null/undefined/invalid item

        const contributors = await unWrapUserAssignees(await item.get('user_contributors_list_custom_user_assignment') || null) || [];
        const authorisers = await unWrapUserAssignees(await item.get('user_authorisers_list_custom_user_assignment') || null) || [];
        const priority_option = async () => {
            const priority = await item.get('priority_option_priority');
            return priority && typeof priority.get === 'function' ? await priority.get('display') : undefined;
        };

        ;
        const matertials_list_custom_material = await unWrapMaterials(await item.get("materials_list_custom_material") || null) || [];

        return {
            _id: await item.get('_id') || null,
            name_text: await item.get('name_text') || '',
            approval_time_number: await item.get('approval_time_number') || 0,
            time_period_number: await item.get('time_period_number') || 0,
            duration: (await item.get('time_period_number') || 0) + (await item.get('approval_time_number') || 0),
            user_contributors_list_custom_user_assignment: contributors,
            contributors: contributors,
            authorisers: authorisers,
            position: await item.get('position_number') || 0,
            dependencies_list_custom_dependency: await unWrapDependencies(await item.get('dependencies_list_custom_dependency') || null) || [],
            is_parallel: await item.get('is_parallel__boolean') || false,
            is_fixed: await item.get('is_fixed_boolean') || false,
            priority_option: await priority_option(),
            start: await item.get('start_date_date') || null,
            end: await item.get('end_date_date') || null,
            matertials_list_custom_material: matertials_list_custom_material
        };
    }

    async function unWrapDependencies(dependencies) {
        if (!dependencies || typeof dependencies.length !== 'function') return []; // Handle null/undefined/invalid dependencies
        const depLength = await dependencies.length();
        if (!depLength || depLength === 0) return []; // Handle empty dependencies
        const depItems = await dependencies.get(0, depLength);

        if (!Array.isArray(depItems)) return [];

        const openedDeps = await Promise.all(depItems.map(async (depItem) => {
            return await extractDependencyData(depItem) || null; // Return null if extract fails
        }));

        return openedDeps.filter(Boolean); // Filter out nulls
    }

    async function extractDependencyData(depItem) {
        if (!depItem || typeof depItem.get !== 'function') return null; // Handle null/undefined/invalid depItem

        const sourceId = await depItem.get('source_id_text') || null;
        const targetId = await depItem.get('target_id_text') || null;
        const activityRef = await depItem.get('activity_custom_activity1') || null;
        const deliverableRef = await depItem.get('deliverable_custom_deliverable2') || null;
        const dataType = deliverableRef ? 'deliverable' : 'activity';
        const obj = await resolveDependencyObject(activityRef || deliverableRef, dataType);

        return {
            _id: await depItem.get('_id') || null,
            source_id: sourceId,
            target_id: targetId,
            type: await depItem.get('type_text') || '',
            is_dependent_into: await depItem.get('is_dependent_into_boolean') ?? false,
            data_type: dataType,
            obj: obj
        };
    }

    async function resolveDependencyObject(ref, dataType) {
        if (!ref || typeof ref.get !== 'function') return null; // Handle null/undefined/invalid ref

        if (dataType === 'activity') {
            return {
                name_text: await ref.get('name_text') || '',
                start_date_date: await ref.get('start_date_date') || null,
                approval_time_number: await ref.get('approval_time_number') || 0,
                workstartdate_date: await ref.get('workstartdate_date') || null,
                workenddate_date: await ref.get('workenddate_date') || null,
                _id: await ref.get('_id') || null
            };
        } else {
            return {
                _id: await ref.get('_id') || null,
                name_text: await ref.get('name_text') || '',
                start: await ref.get('start_date_date') || null,
                end: await ref.get('end_date_date') || null
            };
        }
    }

    async function unWrapCreatorID(creator) {
        if (!creator || typeof creator.get !== 'function') return null; // Handle null/undefined/invalid creator
        return await creator.get('_id') || null;
    }

    async function unWrapUserAssignees(inputList) {
        if (!inputList || typeof inputList.length !== 'function') return []; // Handle null/undefined/invalid inputList
        const listLength = await inputList.length();
        if (!listLength || listLength === 0) return []; // Handle empty list
        const items = await inputList.get(0, listLength);

        return await Promise.all(items.map(async (item) => {
            return item && typeof item.get === 'function' ? await item.get('_id') || null : null;
        })).then(results => results.filter(Boolean)); // Filter out nulls
    }

    function extractAuthorisersAndContributors(list) {
        const result = {
            authorisers: [],
            contributors: []
        };

        if (!Array.isArray(list)) return result; // Handle non-array input

        list.forEach(item => {
            if (item && Array.isArray(item.authorisers)) {
                result.authorisers.push(...item.authorisers);
            }
            if (item && Array.isArray(item.contributors)) {
                result.contributors.push(...item.contributors);
            }
        });

        result.authorisers = [...new Set(result.authorisers.filter(Boolean))];
        result.contributors = [...new Set(result.contributors.filter(Boolean))];

        return result;
    }

    async function processWithRetry(obj, processingFunction) {
        let retries = 0;
        const maxRetries = 20;

        while (retries < maxRetries) {
            try {
                const result = await processingFunction(obj);
                return { success: true, object: result };
            } catch (err) {
                if (err.hasOwnProperty('not_ready_key')) {
                    retries++;
                    // console.debug(`Retry ${retries}/${maxRetries} due to NotReadyError:`, err);
                    await new Promise(resolve => setTimeout(resolve, 500));
                } else {
                    console.error("Unexpected error in processWithRetry:", err);
                    return { success: false, error: err.message };
                }
            }
        }

        return { success: false, error: "Max retries reached; data still not ready" };
    }

    async function unWrapMaterials(materials) {
        if (!materials || typeof materials.length !== 'function') return []; // Handle null/undefined/invalid materials
        const materialsLength = await materials.length();
        if (!materialsLength || materialsLength === 0) return []; // Handle empty materials
        const materialsItems = await materials.get(0, materialsLength);

        if (!Array.isArray(materialsItems)) return [];

        const openedMaterials = await Promise.all(materialsItems.map(async (material) => {
            return await extractMaterialData(material) || null; // Return null if extract fails
        }));

        return openedMaterials.filter(Boolean); // Filter out nulls
    }

    async function extractMaterialData(material) {
        if (!material || typeof material.get !== 'function') return null; // Handle null/undefined/invalid material
        if (!material.get("access_type_option_material_access_type") || material.get("access_type_option_material_access_type").get('display') == "internal_only") return null;


        // Get color from the color option
        const colorOption = await material.get('colour_option_colour');
        const colour = colorOption && typeof colorOption.get === 'function'
            ? await colorOption.get('display')
            : '';

        // Get live dates directly
        const liveDates = await material.get('live_dates_list_date_range') || [];
        const liveDatesItems = extractLiveDates(liveDates);

        return {
            _id: await material.get('_id') || null,
            name_text: await material.get('title1_text') || '',
            repeat: await material.get('is_repeat_boolean') || false,
            colour: colour,
            access_type: await material.get('access_type_option_material_access_type')?.get('display') || '',
            liveDates: liveDatesItems,
            duration: material.get('duration_number') || null,
            period_number: await material.get('period_value_text') || null,
            period_type: await material.get('period_type_option_period_type')?.get('display') || null,
            repeat_type: await material.get('repeat_frequency_typ_text') || null,
            repeat_frequency: await material.get('repeat_freq_value_number') || null,

        };
    }

    function extractLiveDates(liveDates) {
        if (!liveDates || typeof liveDates.length !== 'function') return []; // Handle null/undefined/invalid liveDates
        liveDatesItems = liveDates.get(0, liveDates.length())
        items = liveDatesItems.map(dateRange => dateRange);
        return items.filter(Boolean); // Filter out nulls
    }

    async function extractActivityMaterials(activity) {
        if (!activity || typeof activity.get !== 'function') return []; // Handle null/undefined/invalid activity

        const materialsRef = await activity.get('materials_list_custom_material') || null;
        return await unWrapMaterials(materialsRef) || [];
    }

    // Public API
    return {
        activities_to_json: async function (bn_activities) {
            return await processWithRetry(bn_activities, unWrapActivities);
        },
        activity_to_json: async function (bn_activity) {
            return await processWithRetry(bn_activity, deepUnwrap);
        },
        deliverable_to_json: async function (bn_deliverable) {
            return await processWithRetry(bn_deliverable, extractDeliverableData);
        },
        activity_materials_to_json: async function (bn_activity) {
            return await processWithRetry(bn_activity, extractActivityMaterials);
        }
    };
})();

// Example usage:
const result = await BubbleDataJson.activities_to_json(someActivities);
// const activity = await BubbleDataJson.activity_to_json(singleActivity);
// const deliverable = await BubbleDataJson.deliverable_to_json(singleDeliverable);
// const materials = await BubbleDataJson.activity_materials_to_json(singleActivity);
