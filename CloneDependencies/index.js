async function(properties, context) {
    //const fetch = require('node-fetch');

    function clonedObjDependencies(oldTasks, newTasks) {
        if (oldTasks.length <= 0 || newTasks.length <= 0) return null;
        //Ensure that old are new are equivalent in length
        if (oldTasks.length !== newTasks.length) {
            throw new Error("Both tasks must be equivalent");
        }
        oldTasks.map((ot, index) => { ot.index = index; });
        newTasks.map((nt, index) => { nt.index = index });
        let collection = [];


        const dupDependency = ((n, o) => {
            //create new dup
            const newDepsList = [];
            const getTargetEleFromOriginPattern = ((dep) => {
                // get the id of the dep obj 
                const alikeTaskId = dep.deliverable_custom_deliverable2._id
                // what is the index of the obj in the oldlist
                const alikeTask = oldTasks.find((ot) => ot._id === alikeTaskId);

                // now we have the index, what task in newTasks have same index?
                const originalIndex = alikeTask.index;
                const targetTask = newTasks.find((nt) => nt.index === originalIndex)
                //return the task as the targetTask
                return targetTask
            })

            o.dependencies_list_custom_dependency.forEach(oldDep => {
                const targetObject = getTargetEleFromOriginPattern(oldDep),
                    { type_text, is_dependent_into_boolean } = oldDep,
                    is_predeccessor = type_text === "predecessor",
                    target_id = is_predeccessor ? n._id : targetObject._id,
                    source_id = is_predeccessor ? targetObject._id : n._id;


                //No need for this check, as it will always return false
                if (!is_dependent_into_boolean) {
                    let newDepItem = {
                        target_id: target_id,
                        source_id: source_id,
                        type_text: type_text,
                        deliverable_custom_deliverable2: targetObject
                    }
                    newDepsList.push(newDepItem); // Push transformed values into the new array
                } else {
                    null
                }
            });
            return newDepsList
        })


        for (let i = 0; i < oldTasks.length; i++) {
            const oldItem = oldTasks[i];
            const newItem = newTasks[i];
            let clonedDeps;
            if (oldItem.dependencies_list_custom_dependency.length > 0) {
                try {
                    clonedDeps = dupDependency(newItem, oldItem);

                    //TODO update the newItem.dep list with clonedDeps

                    newItem.dependencies_list_custom_dependency = clonedDeps;
                } catch (error) {
                    throw new Error(error);
                }
            }
        }

        return newTasks;
    }

    async function transformToDeeperList(inputList) {
        // Await the length() call
        const listLength = await inputList.length();

        // Await the get() call and ensure it is properly awaited
        const items = await inputList.get(0, listLength);

        return await Promise.all(items.map(async item => {
            // Fetch only the specified properties for the main item
            const ele = {
                _id: await item.get('_id'),
                name_text: await item.get('name_text'),
                dependencies_list_custom_dependency: await item.get('dependencies_list_custom_dependency')
            };

            // Helper function to extract _pointer._id from an object
            function extractPointerId(obj) {
                return obj && obj._pointer ? obj._pointer._id : null;
            }

            // Helper function to extract only `_id` and `name_text` from the `source` object
            async function extractDeliverableInfo(deliverable) {
                if (!deliverable) return null;
                return {
                    _id: await deliverable.get('_id'),
                    name_text: await deliverable.get('name_text')
                };
            }

            // Process dependencies_list_custom_dependency
            let deps = ele.dependencies_list_custom_dependency;
            let openedDeps = [];

            if (deps) {
                const depLength = await deps.length();
                const depItems = await deps.get(0, depLength);

                openedDeps = await Promise.all(depItems.map(async depItem => {
                    // Fetch only the specified properties for the dependencies
                    const is_dependent_into_boolean = await depItem.get('is_dependent_into_boolean');
                    const activity_custom_activity1 = (await depItem.get('activity_custom_activity1')) ? true : false;
                    const template_custom_activity = await depItem.get('template_custom_activity');

                    // Filter out dependencies based on the conditions
                    if (activity_custom_activity1 || template_custom_activity || is_dependent_into_boolean) {
                        return null; // Mark for filtering
                    }

                    return {
                        _id: await depItem.get('_id'),
                        is_dependent_into_boolean,
                        type_text: await depItem.get('type_text'),
                        source_id_text: extractPointerId(await depItem.get('source_id_text')),
                        target_id_text: extractPointerId(await depItem.get('target_id_text')),
                        deliverable_custom_deliverable2: await extractDeliverableInfo(await depItem.get('deliverable_custom_deliverable2')),
                        activity_custom_activity1,
                        template_custom_activity
                    };
                }));

                // Remove null values (filtered dependencies)
                openedDeps = openedDeps.filter(dep => dep !== null);
            }

            // Update the dependencies in the main object
            ele.dependencies_list_custom_dependency = openedDeps;

            // Return the transformed main object
            return ele;
        }));
    }

    var updateNewTaskWithCloneDepDup = async (newTasks) => {

        try {
            let results = [];
            let hasDependencies = false; // Track if any dependencies exist


            for (const deliverable of newTasks) {
                if (deliverable.dependencies_list_custom_dependency.length > 0) {
                    hasDependencies = true; // Mark that we have at least one deliverable with dependencies
                    const delUrl = `${context.keys['DELIVERABLE_URL']}`.replace('[uuid]', deliverable._id);
                    let createdDepsList = [];

                    for (const dep of deliverable.dependencies_list_custom_dependency) {
                        const depPostBody = {
                            is_dependent_into_boolean: false,
                            source_id_text: dep.source_id,
                            target_id_text: dep.target_id,
                            type_text: dep.type_text,
                            deliverable_custom_deliverable2: dep.deliverable_custom_deliverable2._id,
                            uuid_text: deliverable._id
                        };

                        let res = await postToAPI(depPostBody, `${context.keys['DEPENDENCY_URL']}`);

                        if (res.status !== 'success') {
                            throw new Error(res?.error?.message || "Unknown API error while posting dependency.");
                        }

                        if (res.status === "success") {
                            createdDepsList.push(res.id);
                        }
                    }

                    // Update deliverable after posting all dependencies
                    let updateRes = await postToAPI({ dependencies_list_custom_dependency: createdDepsList }, delUrl, 'PATCH');
                    results.push(updateRes.ok);
                }
            }

            return hasDependencies ? results : ['no deps'];
        } catch (error) {
            throw new Error(error.message || error);
        }
    };

    var postToAPI = async (data = {}, url, action = "POST") => {
        const response = await fetch(url, {
            method: action,
            headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${context.keys['API Key']}`
            },
            body: JSON.stringify(data)
        });

        // Parse JSON response and return
        return action === "POST" ? response.json() : response;
    };
    var santizedOriginal = await transformToDeeperList(properties.original_tasks);
    var santizedCopy = await transformToDeeperList(properties.new_tasks);

    var clonedCopy = clonedObjDependencies(santizedOriginal, santizedCopy);
    var callToApiToUpdateRecord = await updateNewTaskWithCloneDepDup(clonedCopy);


    return {
        cloned_dependencies: callToApiToUpdateRecord.toString()
    }


}