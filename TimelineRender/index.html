<link rel="stylesheet" href="//meta-q.cdn.bubble.io/f1747036634833x188813813511499840/style.css">
<script src="//meta-q.cdn.bubble.io/f1746608848935x306001962074476900/bubbleDataToJson.js"></script>
<script src="//meta-q.cdn.bubble.io/f1736102640020x810919588591463300/graphlib.min.js"></script>
<script src="//meta-q.cdn.bubble.io/f1738084498140x225148080172119300/sort.js"></script>
<script src="//meta-q.cdn.bubble.io/f1742917238485x537644308098058900/calculator.js"></script>
<script src="//meta-q.cdn.bubble.io/f1743593877330x814266030528052200/index.js"></script>
<script src="https://cdn.jsdelivr.net/npm/rrule@2.7.2/dist/es5/rrule.min.js"></script>
<script>window.RRule = window.RRule || rrule.RRule;</script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/luxon/3.5.0/luxon.js"></script>
<script>

    // APIS URLs
    const deliverableUrl = _*_deliverableUrl_*_;
    const dependencyUrl = _*_dependencyUrl_*_;
    const updateActivityEndpoint = _*_updateActivityEndpoint_*_;
    const updateDeliverableEndpoint = _*_updateDeliverableEndpoint_*_;
    const activityUrl = _*_activityUrl_*_;
    const allactivityURL = _*_allactivityUrl_*_;
    const userUrl = _*_userUrl_*_;


    /**VARIABLES */
    var day = 24 * 36e5, // milliseconds in a day
        today = Math.floor(Date.now() / day) * day;  // current day in milliseconds

    var detailChart;
    var image_assets = {
        avatar1_icon: 'https://69906f258885a4f4ebf7e70d3efa75db.cdn.bubble.io/f1721643639754x508379335165734850/avatar_1.svg?_gl=1*by4238*_gcl_aw*R0NMLjE3MTYzNzQxMjYuQ2p3S0NBandyN2F5QmhBUEVpd0E2RUlHeEFmWDBJbG1DTEJHU3NnNnB1TnZ0OUpqUzRCMkR1d290N3VOTmp5ZHV0WTd5bURaaEMtT1R4b0MxY29RQXZEX0J3RQ..*_gcl_au*Nzk1NDMyODI4LjE3MTYxMzI0OTE.*_ga*MTE5NjEwNzM1OC4xNzE1MzM0ODg1*_ga_BFPVR2DEE2*MTcyMTYyOTYwNy4zNS4xLjE3MjE2NTE3OTQuNjAuMC4w',
        avatar2_icon: 'https://69906f258885a4f4ebf7e70d3efa75db.cdn.bubble.io/f1721643644272x357221151790066300/avatar_2.svg?_gl=1*gg8key*_gcl_aw*R0NMLjE3MTYzNzQxMjYuQ2p3S0NBandyN2F5QmhBUEVpd0E2RUlHeEFmWDBJbG1DTEJHU3NnNnB1TnZ0OUpqUzRCMkR1d290N3VOTmp5ZHV0WTd5bURaaEMtT1R4b0MxY29RQXZEX0J3RQ..*_gcl_au*Nzk1NDMyODI4LjE3MTYxMzI0OTE.*_ga*MTE5NjEwNzM1OC4xNzE1MzM0ODg1*_ga_BFPVR2DEE2*MTcyMTYyOTYwNy4zNS4xLjE3MjE2NTE3OTQuNjAuMC4w',
        calendar_icon: 'https://69906f258885a4f4ebf7e70d3efa75db.cdn.bubble.io/f1721643522055x927944309165966200/dependent_out.svg?_gl=1*gg8key*_gcl_aw*R0NMLjE3MTYzNzQxMjYuQ2p3S0NBandyN2F5QmhBUEVpd0E2RUlHeEFmWDBJbG1DTEJHU3NnNnB1TnZ0OUpqUzRCMkR1d290N3VOTmp5ZHV0WTd5bURaaEMtT1R4b0MxY29RQXZEX0J3RQ..*_gcl_au*Nzk1NDMyODI4LjE3MTYxMzI0OTE.*_ga*MTE5NjEwNzM1OC4xNzE1MzM0ODg1*_ga_BFPVR2DEE2*MTcyMTYyOTYwNy4zNS4xLjE3MjE2NTE3OTQuNjAuMC4w',
        dependent_diamond_in_icon: 'https://69906f258885a4f4ebf7e70d3efa75db.cdn.bubble.io/f1721719449243x273223181152114620/dependent_ext.svg?_gl=1*pxon0t*_gcl_aw*R0NMLjE3MTYzNzQxMjYuQ2p3S0NBandyN2F5QmhBUEVpd0E2RUlHeEFmWDBJbG1DTEJHU3NnNnB1TnZ0OUpqUzRCMkR1d290N3VOTmp5ZHV0WTd5bURaaEMtT1R4b0MxY29RQXZEX0J3RQ..*_gcl_au*Nzk1NDMyODI4LjE3MTYxMzI0OTE.*_ga*MTE5NjEwNzM1OC4xNzE1MzM0ODg1*_ga_BFPVR2DEE2*MTcyMTcwMDk5Ni4zNi4xLjE3MjE3MTkyNTIuMjguMC4w'
    }

    let allowChartUpdate = true;
    // OPTIONS
    var options = {
        chart: {
            plotBackgroundColor: '#FFFFFF',
            plotBorderColor: '#787680',
            plotBorderWidth: 1,
            height: 338,
            id: "timeX",
            scrollablePlotArea: {
                minHeight: 250,
            },
            events: {
                load: function () {
                    this.plotBorder.attr({
                        rx: 18,
                        ry: 18
                    })
                },
                render: function () {
                    if (allowChartUpdate) {
                        allowChartUpdate = false;
                        this.update({
                            chart: {
                                scrollablePlotArea: {
                                    minHeight: this.seriesGroup.getBBox(true).height + this.plotTop + 100
                                }
                            }
                        });

                        allowChartUpdate = true;
                    }
                }
            },
            backgroundColor: '#FAF9FB',
            style: {
                // fontFamily: 'lato',
                fontSize: 12
            },
            marginRight: 20,
            spacingRight: 10
        },
        scrollbar: {
            enabled: true,
            trackBackgroundColor: '#f0e6ff',
            barBackgroundColor: '#6b4ea1',
            trackBorderColor: '#f0e6ff'
        },
        rangeSelector: {
            enabled: true,
            selected: 0
        },
        exporting: {
            enabled: false
        },
        plotOptions: {
            series: {
                borderRadius: '50%',
                borderWidth: 0.5,
                borderColor: '#000',
                connectors: {
                    dashStyle: 'Solid',
                    lineColor: '#3E5F90',
                    lineWidth: 2,
                    radius: 5,
                    startMarker: {
                        enabled: false
                    }
                },
                groupPadding: .14,
                dataLabels: [
                    {
                        enabled: true,
                        align: 'left',
                        crop: false,
                        allowOverlap: true,
                        useHTML: true,
                        formatter: function () {
                            if (this.point.task_type == "material_live_event") {
                                // Render different HTML elements based on shape
                                const shape = this.point.shape || 'circle';
                                const color = this.point.color ||
                                    (this.point.completed && this.point.completed.fill) ||
                                    'grey';

                                let shapeHtml = '';

                                if (shape === 'circle') {
                                    // Render a circle with black border
                                    shapeHtml = `<div class="material-shape circle" style="background-color: ${color};"></div>`;
                                } else if (shape === 'triangle') {
                                    // Create a triangle using a div with border trick
                                    shapeHtml = `<div class="material-shape triangle" style="border-bottom-color: ${color};"></div>`;
                                } else {
                                    // For blocks, just show the name
                                    shapeHtml = '';
                                }

                                return `<div class="material-event-container">
            ${shapeHtml}
            <span class='a_name'>${this.point.name}</span>
        </div>`;
                            }
                            // Original code for other point types
                            var dependency_icon = '', dependency_modal = '', is_external_dep_present, dependency_css_string;
                            is_external_dep_present = !this.point.is_type_active_period && this.point.dependencies && this.point.dependencies.length > 0 && this.point.task_type == 'deliverable';
                            var is_type_active_period = this.point.is_type_active_period
                            var authorisers = this.point.assignees.authorisers || []
                            var contributors = this.point.assignees.contributors || []
                            var authorisers_counts = authorisers.length > 1 ? `<span class='custom-icon'>${authorisers.length - 1}+</span>` : ``;
                            var contributors_count = contributors.length > 1 ? `<span class='custom-icon'>${contributors.length - 1}+</span>` : ``;

                            //get width of the rendered point
                            var width = this.shapeArgs.width - 15 //fix overflowing issue.
                            var height = this.shapeArgs.height;

                            var authorisers_icon = authorisers.length > 0
                                ? `<img src="${authorisers[0].icon}" title="${authorisers[0].name}" class="">`
                                : '';

                            var contributors_icon = contributors.length > 0
                                ? `<img src="${contributors[0].icon}" title="${contributors[0].name}" class="">`
                                : '';
                            var priority = this.priority ? this.priority : '';
                            const eventElement = ` <div class="commet-trail-container" style="max-width: ${width}px; height: ${height}px">
                                            <div class="item">${this.point.name}</div>
                                            <div class="commet-middle">
                                                <div class="commet-icon-container">
                                                    ${authorisers_icon}
                                                    ${authorisers_counts}
                                                </div>
                                                <div class="commet-icon-container">
                                                    ${contributors_icon}
                                                    ${contributors_count}
                                                </div>
                                            </div>
                                            <div class="commet-last-child">${priority}</div>
                            </div>`


                            var infoBar = this.point.is_type_active_period ? `<span class='a_name'>${this.point.name}</span>` : eventElement

                            if (is_external_dep_present) {
                                dependency_icon = display_icon(this.point).left_icon;
                                dependency_css_string = 'dependency';
                                dependency_modal = dependency_modal_element(this.point)
                            }
                            // if (is_external_dep_present && has_external_dependent(this.point)) {
                            //     dependency_modal = dependency_modal_element(this.point)

                            // }


                            return `
                          <div class="pointer-first-part ${dependency_css_string} ${pointerCursor(true)}">
                            ${dependency_icon}
                            ${dependency_modal}
                            ${infoBar}
                          </div>
                    `;
                        },
                        padding: 10,
                        style: {
                            fontWeight: 'bold',
                            textOutline: 'none',
                            height: 60
                        }
                    },
                    {
                        enabled: true,
                        align: 'center',
                        useHTML: true,
                        formatter: function () {
                            if (this.point.task_type == "material_live_event") {
                                return ``
                            }
                            var is_type_active_period = this.point.is_type_active_period
                            var authorisers = this.point.assignees.authorisers || []
                            var contributors = this.point.assignees.contributors || []
                            var authorisers_counts = authorisers.length > 1 ? `<span class='custom-icon'>${authorisers.length - 1}+</span>` : ``;
                            var contributors_count = contributors.length > 1 ? `<span class='custom-icon'>${contributors.length - 1}+</span>` : ``;

                            var authorisers_icon = authorisers.length > 0
                                ? `<img src="${authorisers[0].icon}" title="${authorisers[0].name}" class="custom-icon">`
                                : '';

                            var contributors_icon = contributors.length > 0
                                ? `<img src="${contributors[0].icon}" title="${contributors[0].name}" class="custom-icon">`
                                : '';

                            var contributor_div = !is_type_active_period ? `  <div class="contributor flex-container ${pointerCursor(true)}">
                                <div class="flex-child">
                                     <div class="custom-icons">
                                        ${authorisers_icon}
                                        ${authorisers_counts}
                                    </div>
                                    </div>
                                    <div class="divider"></div>
                                    <div class="flex-child">
                                    <div class="custom-icons">
                                        ${contributors_icon}
                                        ${contributors_count}
                                    </div>
                                    </div>
                                </div>` : '';

                            return contributor_div
                        },
                        padding: 10,
                        style: {
                            fontWeight: 'bold',
                            textOutline: 'none'
                        }
                    },
                    {
                        enabled: true,
                        align: 'right',
                        formatter: function () {
                            if (this.point.task_type == "material_live_event") {
                                return ``
                            }
                            var dependency_icon = '';
                            var dependency_modal = '';
                            var is_external_dep_present, dependency_css_string;
                            is_external_dep_present = !this.point.is_type_active_period && this.point.dependencies && this.point.dependencies.length > 0;

                            if (is_external_dep_present) {
                                dependency_icon = display_icon(this.point).right_icon;
                                dependency_css_string = 'dependency'
                            }
                            if (is_external_dep_present && has_external_dependent(this.point)) {
                                dependency_modal = dependency_modal_element(this.point)

                            }

                            return `
                          <div class="pointer-third-part ${dependency_css_string}">
                            ${dependency_icon}
                            ${dependency_modal}
                          </div>
                    `;

                        },
                        useHTML: true,
                        padding: 10,
                        style: {
                            fontWeight: 'normal',
                            textOutline: 'none',
                            opacity: 0.8
                        }
                    }
                ],
                point: {
                    events: {
                        click: function (e) {

                            if (this.task_type == 'activity') {
                                renderSubEvents(this.series.chart, this.event_name, { deliverables: this.deliverables || [], materials: this.material_live_events || [] }, 'deliverable')
                            }
                            if (this.task_type == 'deliverable') {
                                const cloneObj = createTsNewObject(this),
                                    deliverabeTimeObj = JSON.stringify(cloneObj);

                                bubble_fn_showDeliverableDetails({ value: this.id, output1: deliverabeTimeObj })
                            }
                            if (this.task_type == 'live_event') {
                                renderSubEvents(this.series.chart, this.event_name, { deliverables: this.deliverables || [], materials: this.material_live_events || [] }, 'material')
                            }
                            if (this.task_type == 'material_live_event') {
                                const materialId = this.id;

                                bubble_fn_showMaterialLiveEventDetails(materialId)
                            }

                        }
                    }
                }
            }
        },
        series: [],
        tooltip: {
            pointFormat: '<span style="font-weight: bold">{point.name}</span><br>' +
                '{point.start:%e %b}' +
                '{#unless point.milestone} → {point.end:%e %b}{/unless}' +
                '<br>',
            useHTML: true,
            backgroundColor: '#ebeef3',
            borderWidth: 0,
            shadow: '0px 3px 3px red',

            style: {
                zIndex: 9000,
            },
            className: 'activity_deliverables-tooltip',
            enabled: false,
            formatter: function () {
                var deliverable1 = deliverable_count(100);
                var deliverable2 = deliverable_count(100);
                return `
                <div class="activity_deliverables-tooltip container">
                    <div class="full-screen"><img src="./img/Subtract.svg" alt="expamd view"></div>
                    <div class="progress-bar-container">
                        ${deliverable1}
                        ${deliverable1}
                    </div>
                    <div class="group-buttons">
                        <button>Show more</button>
                        <button>Close </button>
                    </div>

                </div>
                `;
            }
        },
        title: {
            text: ''
        },
        xAxis: [{
            currentDateIndicator: {
                color: '#ED2224',
                dashStyle: 'Shortdot',
                width: 3,
                label: {
                    format: ''
                }
            },
            dateTimeLabelFormats: {
                day: '%e<br><span style="opacity: 0.5; font-size: 0.7em">%a</span>'
            },
            grid: {
                borderWidth: 0
            },
            gridLineWidth: 2,
            min: today - 7 * day,
            max: ((today - 7 * day) + 30 * day),
            custom: {
                today,
                weekendPlotBands: true
            },
            gridLineColor: '#FDF7FF',
            labels: {
                padding: 60,
                style: {
                    fontSize: '12px',
                    fontFamily: 'Lato'
                }
            },
            // min: 5,
            // max: 2,
            // scrollbar: { enabled: true}
        }],
        yAxis: {
            type: 'category',
            staticScale: 40,
            visible: false,
            max: 5
        },
        accessibility: {
            enabled: false,
            keyboardNavigation: {
                seriesNavigation: {
                    mode: 'serialize'
                }
            },
            point: {
                descriptionFormatter: function (point) {
                    var completedValue = point.completed ?
                        point.completed.amount || point.completed : null,
                        completed = completedValue ?
                            ' Task ' + Math.round(completedValue * 1000) / 10 +
                            '% completed.' :
                            '',
                        dependency = point.dependency &&
                            point.series.chart.get(point.dependency).name,
                        dependsOn = dependency ?
                            ' Depends on ' + dependency + '.' : '';

                    return Highcharts.format(
                        point.milestone ?
                            '{point.yCategory}. Milestone at {point.x:%Y-%m-%d}. ' +
                            'Owner: {point.owner}.{dependsOn}' :
                            '{point.yCategory}.{completed} Start ' +
                            '{point.x:%Y-%m-%d}, end {point.x2:%Y-%m-%d}. Owner: ' +
                            '{point.owner}.{dependsOn}',
                        { point, completed, dependsOn }
                    );
                }
            }
        },
        lang: {
            accessibility: {
                axis: {
                    xAxisDescriptionPlural: 'The chart has a two-part X axis ' +
                        'showing time in both week numbers and days.'
                }
            }
        }
    };


    var generate_dependecies = ((t, i, tn) => {
        d = {
            id: Math.random(10),
            source_id: 'deliverable_1',
            target_id: i,
            type: t,
            target_name: tn,
        }
        return d
    })

    var deliverable_count = (count) => {
        return `<div class="progress-item ${count}%" style="width: '${count}%';">
                        <div class="progress-bar">
                            <div class="progress">
                                <span class="progress-text">Deliverable 1</span>

                                <div class="contributor flex-container">
                                    <div class="flex-child">
                                        <div class="custom-icons">
                                            <img src="./img/avatar_1.svg" title="David" class="custom-icon">
                                            <span class="custom-icon"></span>
                                        </div>
                                </div>
                                    <div class="divider"></div>
                                    <div class="flex-child">
                                        <div class="custom-icons">
                                            <img src="./img/avatar_1.svg" title="David" class="custom-icon">
                                            <span class="custom-icon"></span>
                                        </div>
                                    </div>
                                </div>
                                <span></span>
                            </div>
                        </div>
                        </div>`
    }

    // Initiators
    document.addEventListener('DOMContentLoaded', () => {
        // Plug-in to render plot bands for the weekends
        Highcharts.addEvent(Highcharts.Axis, 'foundExtremes', e => {
            if (e.target.options.custom && e.target.options.custom.weekendPlotBands) {
                var axis = e.target,
                    chart = axis.chart,
                    day = 24 * 36e5,
                    isWeekend = t => /[06]/.test(chart.time.dateFormat('%w', t)),
                    plotBands = [];

                let inWeekend = false;

                for (
                    let x = Math.floor(axis.min / day) * day;
                    x <= Math.ceil(axis.max / day) * day;
                    x += day
                ) {
                    var last = plotBands.at(-1);
                    if (isWeekend(x) && !inWeekend) {
                        plotBands.push({
                            from: x,
                            color: {
                                pattern: {
                                    path: 'M 0 10 L 10 0 M -1 1 L 1 -1 M 9 11 L 11 9',
                                    width: 10,
                                    height: 10,
                                    color: '#FDF7FF',
                                    backgroundColor: 'rgba(253, 247, 255, 1)'
                                }
                            }
                        });
                        inWeekend = true;
                    }

                    if (!isWeekend(x) && inWeekend && last) {
                        last.to = x;
                        inWeekend = false;
                    }
                }
                axis.options.plotBands = plotBands;
            }
        });

    });

    // TIMELINE FUNCTION
    function dependency_modal_element(point) {
        var dependencies_text = display_message(point)
        return `
        <div class="modal ${point.status}">
            <div class="inner-content">
                <div>
                    <div class="text-bold">External dependency(ies):</div>
                    <div class="inline-flex">
                        ${dependencies_text}
                    </div>
                </div>
            </div>

        </div>
        `
    }

    function contributor_div(assignees) {
        var assigner_counts = `<span class='custom-icon'></span>`
        assignees = assignees.map(assignee => `<img src="${assignee.icon}" title="${assignee.name}" class="custom-icon">`).join('');
        return `  <div class="contributor flex-container">
        <div class="flex-child">
             <div class="custom-icons">
                ${assignees}
                ${assigner_counts}
            </div>
            </div>
            <div class="divider"></div>
            <div class="flex-child">
            <div class="custom-icons">
                ${assignees}
                ${assigner_counts}
            </div>
            </div>
        </div>`;
    }

    // APICALLS  x
    // Helper function to fetch JSON data
    const fetchJson = async (url) => {
        try {
            const response = await fetch(url);
            if (response.ok) {
                return await response.json();
            }
        } catch (error) {
            console.error(`Error fetching data from ${url}:`, error);
        }
        return null;
    };

    function fetchApiResponse(apiUrl, constraints = [], additional_sort_fields = [], limit = null, cursor = null, headers = { 'Content-Type': 'application/json' }) {
        // Utility function to construct query string
        const buildQueryString = (params) => {
            return Object.keys(params)
                .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
                .join('&');
        };

        // Construct query parameters
        const params = {};

        if (constraints && Array.isArray(constraints) && constraints.length > 0) {
            params.constraints = JSON.stringify(constraints);
        }

        if (additional_sort_fields && Array.isArray(additional_sort_fields) && additional_sort_fields.length > 0) {
            params.additional_sort_fields = JSON.stringify(additional_sort_fields);
        }

        if (limit !== null && typeof limit === "string") {
            params.limit = limit;
        }

        if (cursor !== null && typeof cursor === "number") {
            params.cursor = cursor;
        }

        // Convert parameters to query string
        const queryString = buildQueryString(params);
        const fullUrl = queryString ? `${apiUrl}?${queryString}` : apiUrl;


        // Fetch request
        return fetch(fullUrl, {
            method: 'GET',
            headers
        })
            .then(async (response) => {
                const contentType = response.headers.get('Content-Type');
                return response;
            })
            .catch(error => {
                console.error('Fetch Error:', error);
                throw error;
            });
    }

    /**
        * Fetches the name (name_text) for a deliverable or activity by ID.
        * @param {String} id - The ID of the deliverable or activity.
        * @param {String} type - The type of the item ("deliverable" or "activity").
        * @returns {String} The name_text of the deliverable or activity.
        */
    // Function to fetch name by ID
    async function fetchNameById(id, type) {
        let url = type === "deliverable" ? deliverableUrl : activityUrl;
        const data = await fetchJson(url.replace('[uid]', id));
        return data && data.response ? data.response : null;
    }

    // Helper function to fetch dependencies and ensure only resolved objects or null are returned
    async function fetchDependencies(dependenciesList) {
        const results = await Promise.all(
            (dependenciesList || []).map(async (dependencyId) => {
                const dependencyData = await fetchJson(dependencyUrl.replace('[uid]', dependencyId));
                if (dependencyData) {
                    // Await the resolution of fetchNameById to avoid returning a pending promise
                    const objectData = await (dependencyData.response.deliverable_custom_deliverable2
                        ? fetchNameById(dependencyData.response.deliverable_custom_deliverable2, 'deliverable')
                        : fetchNameById(dependencyData.response.activity_custom_activity1, 'activity'));

                    //Hack: build the deliverable activity which
                    let event = await buildActivityObjectForDeliverable(dependencyData.response, objectData);
                    return {
                        _id: dependencyData.response._id,
                        source_id: dependencyData.response.source_id_text,
                        target_id: dependencyData.response.target_id_text,
                        type: dependencyData.response.type_text,
                        is_dependent_into: dependencyData.response.is_dependent_into_boolean,
                        data_type: dependencyData.response.deliverable_custom_deliverable2 ? 'deliverable' : 'activity',
                        obj: objectData, // This will now contain the resolved object or null,
                        activity: event
                    };
                }
                return null; // In case of no dependencyData, return null
            })
        );

        // Filter out null values to return only successful objects
        return results.filter(result => result !== null);
    };

    async function buildActivityObjectForDeliverable(dependency, obj) {
        if (dependency.deliverable_custom_deliverable2 && dependency.is_dependent_into_boolean && obj.activity_custom_activity1) {
            return await fetchNameById(obj.activity_custom_activity1)
        }
    }

    async function fetchAssignments(assignments) {
        const results = await Promise.all(
            (assignments || []).map(async (assignmentId) => {
                const assignmentData = await fetchJson(assignmentUrl.replace('[uid]', assignmentId));
                if (assignmentData) {
                    return {
                        _id: assignmentData.response._id,
                        assigned_role: assignmentData.response.role_option_assigned_role,
                        status: assignmentData.response.status_option_activity_status,
                        user: assignmentData.response.user_user, //TODO return user object here
                        is_activity: assignmentData.response.is_activity_boolean,
                    };
                }
                return null;
            })
        );

        // Filter out null values to return only successful objects
        return results.filter(result => result !== null);
    };

    async function fetchDeliverablesList(deliverablesList) {
        return await Promise.all(
            (deliverablesList || []).map(buildDeliverable)
        );
    }

    async function buildDeliverable(deliverableId) {
        const deliverableData = await fetchJson(deliverableUrl.replace('[uid]', deliverableId));
        if (!deliverableData) return null;

        const dependenciesList = await fetchDependencies(deliverableData.response.dependencies_list_custom_dependency);

        return {
            _id: deliverableData.response._id,
            name_text: deliverableData.response.name_text,
            approval_time_number: deliverableData.response.approval_time_number,
            time_period_number: deliverableData.response.time_period_number,
            duration: (deliverableData.response.time_period_number || 0) + (deliverableData.response.approval_time_number || 0),
            user_contributors_list_custom_user_assignment: deliverableData.response.user_contributors_list_custom_user_assignment || [],
            contributors: deliverableData.response.user_contributors_list_custom_user_assignment || [],
            authorisers: deliverableData.response.user_authorisers_list_custom_user_assignment || [],
            position: deliverableData.response.position_number || 0,
            dependencies_list_custom_dependency: dependenciesList,
            is_parallel: deliverableData.response.is_parallel__boolean || false,
            is_fixed: deliverableData.response.is_fixed_boolean || false,
            priority_option: deliverableData.response.priority_option_priority || undefined,
            start: deliverableData.response.start_date_date,
            end: deliverableData.response.end_date_date
        };
    }

    async function fetchActiveActivitiesData() {
        const constraints = [
            {
                "key": "status_option_activity_status",
                "constraint_type": "equals",
                "value": "Active"
            },
        ];
        const additional_sort_fields = [{ sort_field: "Created Date", descending: false }];
        const limit = '20';

        // Fetch activities
        const activitiesResponse = await fetchApiResponse(allactivityURL, constraints, additional_sort_fields, limit);
        if (!activitiesResponse.ok) {
            throw new Error('Failed to fetch activities');
        }
        const activitiesData = await activitiesResponse.json();
        const activities = activitiesData.response.results;



        // Map activities to the desired format
        const cleanedActivityJsonList = await Promise.all(
            activities.map(async (activity) => {
                try {
                    // Fetch deliverables
                    const deliverablesList = await fetchDeliverablesList(activity.deliverables_list_custom_deliverable2)

                    // Fetch dependencies
                    const dependenciesList = await fetchDependencies(activity.dependencies_list_custom_dependency, activity._id, null)

                    const actors = extractAuthorisersAndContributors(deliverablesList)
                    // TODO convert to good datte format
                    // Return a cleaned activity object
                    return {
                        _id: activity._id,
                        name_text: activity.name_text,
                        approval_time_number: activity.approval_time_number,
                        start_date_date: new Date(activity.start_date_date),
                        end_date_date: activity.end_date_date ? new Date(activity.end_date_date) : null,
                        activity_creator_user: activity['Created By'],
                        deliverables_list_custom_deliverable2: deliverablesList.filter(Boolean),
                        dependencies: dependenciesList.filter(Boolean),
                        work_start_date: activity.workstartdate_date,
                        work_end_date: activity.workenddate_date,
                        authorisers: activity.user_authorisers_list_custom_user_assignment,
                        deliverable_authorisers: actors.authorisers,
                        deliverable_contributors: actors.contributors
                    };
                } catch (error) {
                    console.error("Error processing activity: ", activity._id, error);
                    return null;
                }
            })
        );


        window.localStorage.setItem("fetchedActivities", JSON.stringify(cleanedActivityJsonList.filter(Boolean)));

        return cleanedActivityJsonList.filter(Boolean); // Ensure no null objects are returned
    }

    // SCHEDULING FUNCTIONS
    async function fetchActivityDataByID(id) {
        try {
            // Fetch the activity
            let url = activityUrl;
            const activityData = await fetchJson(url.replace('[uid]', id));
            if (!activityData || !activityData.response) {
                throw new Error('Failed to fetch activity data');
            }
            const activity = activityData.response;


            // Fetch the deliverables for the activity
            const deliverablesList = await fetchDeliverablesList(activity.deliverables_list_custom_deliverable2)

            // Fetch the dependencies for the activity itself
            const dependenciesList = await fetchDependencies(activity.dependencies_list_custom_dependency, activity._id);
            const actors = extractAuthorisersAndContributors(deliverablesList)
            // Return the final full activity object
            const result = {
                _id: activity._id,
                name_text: activity.name_text,
                approval_time_number: activity.approval_time_number,
                start_date_date: new Date(activity.start_date_date),
                end_date_date: activity.end_date_date ? new Date(activity.end_date_date) : null,
                activity_creator_user: activity['Created By'],
                deliverables_list_custom_deliverable2: deliverablesList.filter(Boolean),
                dependencies: dependenciesList.filter(Boolean),
                work_start_date: activity.workstartdate_date,
                work_end_date: activity.workenddate_date,
                authorisers: activity.user_authorisers_list_custom_user_assignment,
                deliverable_authorisers: actors.authorisers,
                deliverable_contributors: actors.contributors
            };

            localStorage.setItem('currentActivity', JSON.stringify(result))
            return result;

        } catch (error) {
            throw new Error(error.message);
        }
    }


    function extractAuthorisersAndContributors(list) {
        const result = {
            authorisers: [],
            contributors: []
        };

        list.forEach(item => {
            // Check if authorisers or contributors exist and are arrays, then merge them
            if (Array.isArray(item.authorisers)) {
                result.authorisers.push(...item.authorisers);
            }
            if (Array.isArray(item.contributors)) {
                result.contributors.push(...item.contributors);
            }
        });

        // Remove duplicates
        result.authorisers = [...new Set(result.authorisers)];
        result.contributors = [...new Set(result.contributors)];

        return result;
    }

    function generateNumberList(arr) {
        length = arr.length
        return Array.from({ length: length }, (v, i) => i);
    }

    function renderSubEvents(chart, superEventName, subEvents, eventType = 'deliverable') {
        const chartContainer = $(`#${chart.renderTo.id}`)[0].parentNode,
            eventChart = $(`#${chart.renderTo.id}`),
            subEventDeliverableChart = $(chartContainer).find('#detailView'),
            subEventMaterialChart = $(chartContainer).find('#liveEventView'),
            toggleFullscreenButton = $(chartContainer).find('#toggle-fullscreen'),
            eventNavButton = $(chartContainer).find('#activity_overview'),
            subEventNavButton = $(chartContainer).find('#active_activity'),
            toggleSubEventViews = $(chartContainer).find('#toggle-sub-event-views'),
            toggleDeliverableButton = toggleSubEventViews.find('#toggle_deliverable_view'),
            toggleLiveButton = toggleSubEventViews.find('#toggle_live_view'),
            deliverableCheckIcon = toggleDeliverableButton.find('span.material-symbols-outlined'),
            liveCheckIcon = toggleLiveButton.find('span.material-symbols-outlined');

        // Ensure subEvents properties are arrays
        const deliverables = subEvents.deliverables || [];
        const materials = subEvents.materials || [];

        let parentElement = $(`#${chart.renderTo.id}`).parent();
        let calculatedHeight = parentElement.hasClass('defaultMode')
            ? parentElement.outerHeight() - 80
            : parentElement.outerHeight();

        // Set background class based on eventType
        if (eventType === 'material') {
            parentElement.addClass('materialView');
        } else {
            parentElement.removeClass('materialView');
        }

        let max = chart.renderTo.id === 'fullChart' ? 20 : 5;

        // Always render deliverables chart, even with empty data
        const deliverableOpts = setUpDeliverableOption(deliverables.length > 0 ? deliverables : [], {
            height: calculatedHeight,
            rangeSelector: true,
            max: max
        });

        const deliverableChartData = {
            ...deliverableOpts,
            series: [{
                name: 'Deliverables Timeline',
                data: deliverables.length > 0 ? deliverables : []
            }]
        };

        // Add empty state message for deliverables
        if (deliverables.length === 0) {
            deliverableOpts.chart.events = {
                load: function () {
                    this.renderer.text('No deliverables available for this activity',
                        this.plotWidth / 2 + this.plotLeft - 100,
                        this.plotHeight / 2 + this.plotTop
                    )
                        .css({
                            color: '#666',
                            fontSize: '14px'
                        })
                        .add();
                }
            };
        }

        Highcharts.ganttChart(subEventDeliverableChart[0], deliverableChartData, function () {
            if (deliverables.length > 0) {
                const firstEvent = deliverables.at(-1);
                const FOUR_DAYS_IN_MS = 3 * 24 * 60 * 60 * 1000;
                const TWENTY_SEVEN_DAYS_IN_MS = 27 * 24 * 60 * 60 * 1000;

                const adjustedStart = firstEvent.start - FOUR_DAYS_IN_MS;
                const adjustedEnd = firstEvent.end + TWENTY_SEVEN_DAYS_IN_MS;
                const chart = (Highcharts.charts || [])
                    .filter(chart => chart)
                    .find(chart => chart.renderTo === subEventDeliverableChart[0]);

                chart.xAxis[0].setExtremes(adjustedStart, adjustedEnd);
            }
        });

        // Always render materials chart, even with empty data
        const materialOpts = setUpDeliverableOption(materials.length > 0 ? materials : [], {
            height: calculatedHeight,
            rangeSelector: true,
            max: max
        });

        // Apply material-specific chart styling
        materialOpts.chart.backgroundColor = '#EAFAF3';
        materialOpts.plotOptions.series.borderRadius = '0%';

        // Add empty state message for materials
        if (materials.length === 0) {
            materialOpts.chart.events = {
                load: function () {
                    this.renderer.text('No material live events available for this activity',
                        this.plotWidth / 2 + this.plotLeft - 120,
                        this.plotHeight / 2 + this.plotTop
                    )
                        .css({
                            color: '#666',
                            fontSize: '14px'
                        })
                        .add();
                }
            };
        }

        const materialChartData = {
            ...materialOpts,
            series: [{
                name: 'Material Live Events Timeline',
                data: materials.length > 0 ? materials : []
            }]
        };

        Highcharts.ganttChart(subEventMaterialChart[0], materialChartData, function () {
            if (materials.length > 0) {
                const firstEvent = materials.at(-1);
                const FOUR_DAYS_IN_MS = 3 * 24 * 60 * 60 * 1000;
                const TWENTY_SEVEN_DAYS_IN_MS = 27 * 24 * 60 * 60 * 1000;

                const adjustedStart = firstEvent.start - FOUR_DAYS_IN_MS;
                const adjustedEnd = firstEvent.end + TWENTY_SEVEN_DAYS_IN_MS;
                const chart = (Highcharts.charts || [])
                    .filter(chart => chart)
                    .find(chart => chart.renderTo === subEventMaterialChart[0]);

                chart.xAxis[0].setExtremes(adjustedStart, adjustedEnd);
            }
        });

        // Show/hide elements based on event type
        subEventNavButton.text(superEventName);
        subEventNavButton.parent().removeClass('hidden');
        eventChart.addClass('hidden');

        // Update visibility and toggle button states based on eventType
        if (eventType === 'material') {
            subEventDeliverableChart.addClass('hidden');
            subEventMaterialChart.removeClass('hidden');
            toggleDeliverableButton.removeClass('active');
            toggleLiveButton.addClass('active');
            deliverableCheckIcon.addClass('hidden');
            liveCheckIcon.removeClass('hidden');
        } else {
            subEventDeliverableChart.removeClass('hidden');
            subEventMaterialChart.addClass('hidden');
            toggleDeliverableButton.addClass('active');
            toggleLiveButton.removeClass('active');
            deliverableCheckIcon.removeClass('hidden');
            liveCheckIcon.addClass('hidden');
        }
    }

    function pointerCursor(t) {
        if (t) {
            return 'pointer-cursor'
        }
    }

    function display_icon(obj) {
        let left_icon = '', right_icon = '';


        var renderButton = function (s, i, t) {
            return `<button class='fab ${s} ${t}'><img src=${i} title="" class="custom-icon"></button>`;
        }

        for (let dep of obj.dependencies) {
            if (dep.is_dependent_into) {
                left_icon = renderButton(obj.status, image_assets.dependent_diamond_in_icon, 'ext');
            }
        }
        return { left_icon, right_icon };
    }

    function display_message(obj) {
        var targetNames = obj.dependencies
            .filter(dep => dep.is_dependent_into) // Only include dependencies where is_external is true
            .map(dep => `${(dep.activity ? dep.activity.name_text + ' : ' : '') + dep.obj.name_text || ''}`) // Map to name_text or an empty string
            .join(', ');
        return `${targetNames}`;
    }

    function has_external_dependent(obj) {
        if (obj.task_type !== 'deliverable') {
            return false;
        }

        return obj.dependencies.some(dep => dep.target_id !== obj.activity_id);
    }

    function getTimelineElements(chartElement) {
        // Get the parent section of the chart element
        const timelineSection = chartElement.container.parentElement.parentElement;

        // Find the specific elements inside the timeline section
        const toggleDependencies = timelineSection.querySelector('#toggle-fullscreen');
        const activeActivityLink = timelineSection.querySelector('#active_activity');
        const overviewLink = timelineSection.querySelector('#activity_overview');

        // Return the elements or do something with them
        return {
            timelineSection,
            toggleDependencies,
            activeActivityLink,
            overviewLink
        };
    }


    function initializeAuxiliaryTimelineFeatures(chart, suppliedEvents) {
        const chartContainer = chart.container.parentNode.parentNode,
            eventChart = $(chartContainer).find(`#${chart.renderTo.id}`),
            subEventChart = $(chartContainer).find('#detailView'),
            subEventLiveChart = $(chartContainer).find('#liveEventView'),
            toggleFullscreenButton = $(chartContainer).find('#toggle-fullscreen'),
            hideFullscreenButton = $(chartContainer).find('#hide-fullscreen'),
            eventNavButton = $(chartContainer).find('#activity_overview'),
            subEventNavButton = $(chartContainer).find('#active_activity'),
            toggleSubEventViews = $(chartContainer).find('#toggle-sub-event-views'),
            toggleDeliverableButton = toggleSubEventViews.find('#toggle_deliverable_view'),
            toggleLiveButton = toggleSubEventViews.find('#toggle_live_view'),
            deliverableCheckIcon = toggleDeliverableButton.find('span.material-symbols-outlined'),
            liveCheckIcon = toggleLiveButton.find('span.material-symbols-outlined');

        toggleFullscreenButton.attr('data-chart-id', chart.renderTo.id);

        // Toggle between Deliverable and Live views
        toggleDeliverableButton.off('click').on('click', function () {
            if (!subEventChart.is(":visible")) {
                subEventLiveChart.addClass('hidden');
                subEventChart.removeClass('hidden');
                toggleLiveButton.removeClass('active');
                toggleDeliverableButton.addClass('active');
                chartContainer.classList.remove('materialView');
                deliverableCheckIcon.removeClass('hidden');
                liveCheckIcon.addClass('hidden');
            }
        });

        toggleLiveButton.off('click').on('click', function () {
            if (!subEventLiveChart.is(":visible")) {
                subEventChart.addClass('hidden');
                subEventLiveChart.removeClass('hidden');
                toggleDeliverableButton.removeClass('active');
                toggleLiveButton.addClass('active');
                chartContainer.classList.add('materialView');
                deliverableCheckIcon.addClass('hidden');
                liveCheckIcon.removeClass('hidden');
            }
        });

        // Fullscreen button handler
        toggleFullscreenButton.off('click').on('click', function () {
            toggleFullScreen(this, chart);
        });

        // Hide fullscreen button handler
        hideFullscreenButton.off('click').on('click', function () {
            hideFullScreen();
        });

        // Back to super event handler
        eventNavButton.off('click').on('click', function () {
            backToSuperEvent();
        });

        function toggleFullScreen(_button, chartInstance) {
            const fullChartModal = 'fullChart';
            showTimelineLoader(fullChartModal, true);
            const seriesData = chartInstance.series;

            backToSuperEvent();

            bubble_fn_fullTimelineModal(true);
            setTimeout(() => {
                const parentElement = document.getElementById(fullChartModal)?.parentElement || $('#fullChart').parent()[0];
                const height = parentElement.offsetHeight - 90;

                switch (chartInstance.renderTo.id) {
                    case 'deliverable':
                        const newOpts = setUpDeliverableOption(suppliedEvents, {
                            height: height,
                            rangeSelector: true,
                            max: 20
                        });
                        const chartData = {
                            ...newOpts,
                            series: [{
                                name: 'Deliverables Timeline',
                                data: suppliedEvents
                            }]
                        };

                        Highcharts.ganttChart(fullChartModal, chartData, function (chart) {
                            initializeAuxiliaryTimelineFeatures(chart);
                            const firstEvent = suppliedEvents.at(-1);
                            const FOUR_DAYS_IN_MS = 3 * 24 * 60 * 60 * 1000;
                            const TWENTY_SEVEN_DAYS_IN_MS = 27 * 24 * 60 * 60 * 1000;

                            const adjustedStart = firstEvent.start - FOUR_DAYS_IN_MS;
                            const adjustedEnd = firstEvent.end + TWENTY_SEVEN_DAYS_IN_MS;
                            chart.xAxis[0].setExtremes(adjustedStart, adjustedEnd);
                        });
                        break;

                    default:
                        const configOptions = setUpChartOption({
                            events: seriesData,
                            max: 20,
                            height: height
                        });

                        Highcharts.ganttChart(fullChartModal, configOptions, function (chart) {
                            initializeAuxiliaryTimelineFeatures(chart);
                        });
                        break;
                }

                showTimelineLoader(fullChartModal, false);
            }, 1500);
        }

        function hideFullScreen() {
            backToSuperEvent();
            bubble_fn_fullTimelineModal(false);
        }

        function backToSuperEvent() {
            if (subEventChart.is(":visible") || subEventLiveChart.is(":visible")) {
                chartContainer.classList.remove('materialView');
                subEventNavButton.text('');
                subEventNavButton.parent().addClass('hidden');
                subEventChart.addClass('hidden');
                subEventLiveChart.addClass('hidden');
                eventChart.removeClass('hidden');
                toggleDeliverableButton.removeClass('active');
                toggleLiveButton.removeClass('active');
                deliverableCheckIcon.removeClass('hidden');
                liveCheckIcon.removeClass('hidden');
            }
        }
    }

    // Specialized auxiliary features initialization for deliverable charts
    function initializeAuxiliaryFeaturesForDeliverableChart(container_id, suppliedEvents) {
        // Find the parent section using the container_id
        const timelineSection = $(`#${container_id}`).closest('section.timeline');

        // Find all required elements within the section
        const detailView = timelineSection.find('#detailView');
        const liveEventView = timelineSection.find('#liveEventView');
        const toggleFullscreenButton = timelineSection.find('#toggle-fullscreen');
        const hideFullscreenButton = timelineSection.find('#hide-fullscreen');
        const eventNavButton = timelineSection.find('#activity_overview');
        const subEventNavButton = timelineSection.find('#active_activity');
        const toggleSubEventViews = timelineSection.find('#toggle-sub-event-views');
        const toggleDeliverableButton = toggleSubEventViews.find('#toggle_deliverable_view');
        const toggleLiveButton = toggleSubEventViews.find('#toggle_live_view');
        const deliverableCheckIcon = toggleDeliverableButton.find('span.material-symbols-outlined');
        const liveCheckIcon = toggleLiveButton.find('span.material-symbols-outlined');

        // Set data attribute for the fullscreen button
        toggleFullscreenButton.attr('data-chart-id', container_id);

        // Toggle between Deliverable and Live views
        toggleDeliverableButton.off('click').on('click', function () {
            if (!detailView.is(":visible")) {
                liveEventView.addClass('hidden');
                detailView.removeClass('hidden');
                toggleLiveButton.removeClass('active');
                toggleDeliverableButton.addClass('active');
                timelineSection.removeClass('materialView');
                deliverableCheckIcon.removeClass('hidden');
                liveCheckIcon.addClass('hidden');
            }
        });

        toggleLiveButton.off('click').on('click', function () {
            if (!liveEventView.is(":visible")) {
                detailView.addClass('hidden');
                liveEventView.removeClass('hidden');
                toggleDeliverableButton.removeClass('active');
                toggleLiveButton.addClass('active');
                timelineSection.addClass('materialView');
                deliverableCheckIcon.addClass('hidden');
                liveCheckIcon.removeClass('hidden');
            }
        });

        // Fullscreen button handler
        toggleFullscreenButton.off('click').on('click', function () {
            toggleFullScreenForSubEvents(this, suppliedEvents);
        });

        // Hide fullscreen button handler
        hideFullscreenButton.off('click').on('click', function () {
            hideFullScreen();
        });

        // Back to super event handler
        eventNavButton.off('click').on('click', function () {
            backToSuperEvent();
        });

        function hideFullScreen() {
            backToSuperEvent();
            bubble_fn_fullTimelineModal(false);
        }

        function backToSuperEvent() {
            if (detailView.is(":visible") || liveEventView.is(":visible")) {
                timelineSection.removeClass('materialView');
                subEventNavButton.text('');
                subEventNavButton.parent().addClass('hidden');
                detailView.addClass('hidden');
                liveEventView.addClass('hidden');
                timelineSection.find(`#${container_id}`).removeClass('hidden');
                toggleDeliverableButton.removeClass('active');
                toggleLiveButton.removeClass('active');
                deliverableCheckIcon.removeClass('hidden');
                liveCheckIcon.removeClass('hidden');
            }
        }

        function toggleFullScreenForSubEvents(chart, timeseries) {
            const fullChartModal = 'fullChart';
            showTimelineLoader(fullChartModal, true);
            // backToSuperEvent();
            bubble_fn_fullTimelineModal(true);
            setTimeout(() => {
                renderTimelineToDeliverableChart(timeseries, fullChartModal)
            }, 1000);

        }
    }

    // HELPER FUNCTIONS
    /**
    * Calculates the date of a given number of workdays before a specified date, excluding weekends.
    * @param {Date} endDate - The end date from which to calculate backward.
    * @param {number} workDays - The number of workdays to subtract.
    * @returns {Date} The calculated start date.
    *
    */

    // Utility function to convert a JavaScript Date object to UTC Unix timestamp (in seconds)
    function dateToUTCTimestamp(dateString) {
        // Create a new Date object from the input string
        let date = new Date(dateString);

        // Convert the date to a Unix timestamp in milliseconds
        return date.getTime();
    }

    // Helper function to get full name of a user
    function getFullName(user) {
        const firstName = user.first_name_text || '';
        const lastName = user.last_name_text || '';
        return `${firstName} ${lastName}`.trim(); // Trim to remove extra spaces if one name is missing
    }

    /**
    * Generates a style configuration for an activity based on its status and progress.
    *
    * @param {string} status - The status of the activity, which can be 'green', 'amber', or 'red'.
    *                          If an unknown status is passed, 'green' will be used as the default.
    * @param {number} progress - A number between 0 and 1 representing the progress of the activity.
    *                            Values less than 0 will be set to 0, and values greater than 1 will be set to 1.
    *
    * @returns {object} An object containing the color gradient, completion progress, and status information for the activity.
    *
    * The returned object has the following structure:
    * - `colour`: Defines a linear gradient used to represent the activity's progress.
    *   - `linearGradient`: Object with x1, x2, y1, y2 values defining the gradient's direction.
    *   - `id`: A unique ID for the gradient, generated using a random string.
    *   - `stops`: Array defining the gradient stops, with colors dynamically generated based on the status and progress.
    * - `completed`: Object representing the progress completion.
    *   - `amount`: The progress value clamped between 0 and 1.
    *   - `fill`: The color associated with the status.
    * - `status`: The status of the activity, passed from the input parameter.
    */
    function getItemStyle(status, progress) {
        // Define status colors for green, amber, and red
        const colors = {
            green: "#D5F6E8",
            amber: "#FFE5B4",
            red: "#FFCCCB"
        };

        // Get the color associated with the status, default to green if not found
        const color = colors[status] || colors.green;

        // Clamp progress between 0 and 1
        const amount = Math.min(Math.max(progress, 0), 1);

        // Function to convert a hex color code to an RGB string
        function hexToRgb(hex) {
            const bigint = parseInt(hex.slice(1), 16); // Remove '#' and convert hex to integer
            const r = (bigint >> 16) & 255; // Extract red component
            const g = (bigint >> 8) & 255;  // Extract green component
            const b = bigint & 255;         // Extract blue component
            return `${r}, ${g}, ${b}`;      // Return as a comma-separated string
        }

        return {
            colour: {
                // Define a horizontal linear gradient (left to right)
                linearGradient: {
                    x1: 0,
                    x2: 1,
                    y1: 0,
                    y2: 0,
                    id: `highcharts-${Math.random().toString(36).substr(2, 9)}` // Unique ID for the gradient
                },
                // Define the color stops for the gradient
                stops: [
                    [0, color], // Start with the status color
                    [amount, color], // Keep the status color up to the progress amount
                    [amount, `rgba(${hexToRgb(color)}, 0.5)`], // Add a semi-transparent version of the color at the progress point
                    [0.5, `rgba(${hexToRgb(color)}, 0.5)`], // Add a midpoint stop with semi-transparent color
                    [0.5, "#fff"], // Transition to white at the midpoint
                    [1, "#fff"] // End the gradient with white
                ]
            },
            completed: {
                amount: amount, // Return the clamped progress value
                fill: color // Fill color based on the status
            },
            status: status // Return the original status
        };
    }

    function convertToDate(dateString) {
        const [day, month, year] = dateString.split('/');
        return new Date(`${year}-${month}-${day}`);
    }

    function convertIsoToDate(isoString) {
        const date = new Date(isoString);
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-based
        const year = date.getFullYear();

        return convertToDate(`${day}/${month}/${year}`);
    }

    // Helper function to map dependencies
    function mapDependencies(dependencies) {
        return dependencies.map(dep => ({
            id: dep._id,
            source_id: dep.source_id,
            target_id: dep.deliverable || dep.activity, // Handles both deliverable and activity as target_id
            target_date: dep.obj,  // Assumes obj holds the date or a valid representation of the target
            type: dep.is_dependent_into ? "dependent" : "independent", // Using is_dependent_into for type
        }));
    }

    function convertToJSformat(date) {
        // Convert DD/MM/YY or DD/MM/YYYY format to MM/DD/YYYY format if necessary
        if (typeof date === 'string') {
            const [day, month, year] = date.split('/');
            const fullYear = year.length === 2 ? `20${year}` : year;  // Handle 2-digit year as well
            date = `${month}/${day}/${fullYear}`;  // Convert to MM/DD/YYYY
        }
        return date
    }

    function generateTimeSeries(activity, index, currentEventId) {
        let isCurrentEvent = (currentEventId && currentEventId.id === activity._id && currentEventId.type === 'activity') ? true : false
        let borderWidth = isCurrentEvent ? 3 : 0.5;
        let borderColor = isCurrentEvent ? '#64558F' : '#3E5F90';


        let activityStyle = getItemStyle("green", 0.2);

        // Set the default timeline for the activity
        let activityTimeline = {
            id: `Id${activity._id}`,
            name: activity.name_text,
            is_type_active_period: false,
            start: (activity.work_start_date === null || !activity.deliverables_list_custom_deliverable2?.length)
                ? null
                : dateToUTCTimestamp(activity.work_start_date),
            end: (activity.work_start_date === null || !activity.deliverables_list_custom_deliverable2?.length)
                ? null
                : dateToUTCTimestamp(activity.start_date_date),
            completed: activityStyle.completed,
            color: activityStyle.colour,
            owner: activity.activity_creator_user,
            dependency: getExternalDependencies(activity.dependencies),
            is_starred: false,
            assignees: deriveAssigns(activity, 'activity'),
            status: activityStyle.status,
            y: index,
            has_dependant: activity.dependencies && activity.dependencies.length > 0,
            zIndex: 1,
            task_type: "activity",
            className: "pointer-cursor",
            deliverables: [],
            dependencies: activity.dependencies,
            borderWidth: borderWidth,
            borderColor: borderColor,
            is_current_event: isCurrentEvent,
            material_live_events: [],
            event_name: activity.name_text
        };


        // Loop over deliverables and generate timelines for each
        activity.deliverables_list_custom_deliverable2.forEach((deliverable, index, array) => {
            const reversedIndex = array.length - 1 - index; // Calculate the reversed index
            const deliverableTimeline = generateDeliverableTimeline(deliverable, activity, reversedIndex, currentEventId);
            activityTimeline.deliverables.push(deliverableTimeline);
        });


        // Extract material live events from deliverables
        //TODO extract material live events from deliverables,
        const material_live_events = !activity.end_date_date ? [] : extractMaterialLiveEventsFromDeliverables(activity.deliverables_list_custom_deliverable2);

        // Add material live events to the activity timeline
        activityTimeline.material_live_events = material_live_events;

        // Create the milestone "launch" object
        let launchMilestone;
        var endLaunchDate;
        let baseMilestone = {
            //    id: `${activity._id}_`,  // Append underscore to activity ID
            is_type_active_period: true,
            start: dateToUTCTimestamp(activity.start_date_date),  // Live date
            owner: activity.activity_creator_user,
            assignees: [],
            y: index,
            borderWidth: borderWidth,
            className: "custom-point-1",
            borderColor: borderColor,
            zIndex: 1,
            milestone: false,  // Default is false, can be updated later,
            event_name: activity.name_text
        };

        // Conditional logic for launch or LIVE milestones
        if (!activity.end_date_date) {
            launchMilestone = {
                ...baseMilestone,
                name: "",
                zIndex: 1,
                milestone: true,
                connectors: {
                    dashStyle: 'dash',
                    lineColor: '#C8C5D0',
                    lineWidth: 2,
                    radius: 5,
                    startMarker: {
                        enabled: false
                    }
                },
                dependency: activity._id, //change to using 'Id' prefix to fix line not showing
                material_live_events: []

            };
        } else {
            launchMilestone = {
                ...baseMilestone,
                name: "LIVE",
                end: activity.end_date_date ? dateToUTCTimestamp(activity.end_date_date) : undefined, //
                completed: {
                    amount: 1,
                    fill: "#D5E3FF"
                },
                milestone: false,
                task_type: "live_event",
                material_live_events: material_live_events,
                deliverables: activityTimeline.deliverables
            };
        }

        // Return the final timeline objects
        return { activityTimeline, launchMilestone };
    }

    async function generateDeliverableTimeseries(id) {
        const deliverable = await buildDeliverable(id);
        // TODO RENAME OR INVESTIGATE THIS FX
        return generateDeliverableTimeline(deliverable,)

    }

    function generateDeliverableTimeline(deliverable, activity, index, currentEventId) {
        const deliverableStyle = getItemStyle("green", 0.2);
        const start = convertIsoToDate(deliverable.start);
        const end = convertIsoToDate(deliverable.end);
        const position = typeof (deliverable?.position) !== 'undefined' ? deliverable.position : index;

        const generateRandomColor = () => {
            // Generate a random hex color
            const randomColor = `#${Math.floor(Math.random() * 16777215).toString(16)}`;
            return randomColor;
        };

        const priorityToLevelTxt = (priority) => {
            const priorityMap = {
                'Critical (P1)': 'P1',
                'High (P2)': 'P2',
                'Medium (P3)': 'P3',
                'Low (P4)': 'P4',
                'Negligible (P5)': 'P5'
            };
            return priorityMap[priority] || ''; // Default to Medium (P3) for unknown priorities
        };
        let isCurrentEvent = (currentEventId && currentEventId.id === deliverable._id && currentEventId.type === 'deliverable') ? true : false
        const DborderWidth = isCurrentEvent ? 3 : 0.5;

        return {
            id: deliverable._id,
            name: deliverable.name_text,
            activity_id: activity?._id,
            start: dateToUTCTimestamp(start),  // Convert to UTC timestamp
            end: dateToUTCTimestamp(end),  // Convert to UTC timestamp
            dependency: getInternalDependencies(deliverable.dependencies_list_custom_dependency),
            completed: deliverableStyle.completed,
            color: deliverableStyle.colour,
            owner: activity?.activity_creator_user,
            assignees: deriveAssigns(deliverable, 'deliverable'),
            status: deliverableStyle.status,
            has_dependant: deliverable.dependencies_list_custom_dependency.length > 0,
            zIndex: 1,
            task_type: "deliverable",
            dependencies: deliverable.dependencies_list_custom_dependency,
            y: index,
            borderWidth: DborderWidth,
            borderColor: '#64558F',  // Custom border color for this point
            priority: priorityToLevelTxt(deliverable.priority_option),
            is_current_event: isCurrentEvent,
            connectors: {
                lineColor: generateRandomColor(),
            },
            // deliverableMaterialsEvents: deliverableMaterialsLiveEvents
        };
    }

    function generateMaterialLiveEvents(materials) {
        // Handle empty or undefined materials
        if (!materials || !Array.isArray(materials) || materials.length === 0) {
            return []; // Return empty array if no materials
        }

        const availableColors = ['grey', 'brown', 'red', 'yellow', 'orange', 'pink', 'purple', 'green', 'blue', 'black'];
        let lastUsedColor = null;

        // Sort materials by the start date of their first date range, then alphabetically
        const sortedMaterials = [...materials].sort((a, b) => {
            // Skip invalid materials
            if (!a.liveDates?.length || !b.liveDates?.length) {
                return 0;
            }

            // Get first date range for each material
            const aFirstRange = a.liveDates[0];
            const bFirstRange = b.liveDates[0];

            // Skip invalid date ranges
            if (!Array.isArray(aFirstRange) || !Array.isArray(bFirstRange) ||
                aFirstRange.length < 1 || bFirstRange.length < 1) {
                return 0;
            }

            // Compare start dates
            const aStartDate = new Date(aFirstRange[0]).getTime();
            const bStartDate = new Date(bFirstRange[0]).getTime();

            if (aStartDate !== bStartDate) {
                return aStartDate - bStartDate; // Sort by earliest start date
            }

            // If start dates are the same, sort alphabetically by name
            return (a.name_text || '').localeCompare(b.name_text || '');
        });

        // Process the sorted materials
        return sortedMaterials.flatMap((material, materialIndex) => {
            // Skip invalid materials or those without liveDates
            if (!material || !material.liveDates || !Array.isArray(material.liveDates) || material.liveDates.length === 0) {
                return [];
            }

            // Get specified color or prepare to select random color
            let specifiedColor = material.colour?.toLowerCase();

            if (!specifiedColor) {
                // Filter out the last used color to avoid repetition
                const availableChoices = availableColors.filter(color => color !== lastUsedColor);
                // Select a random color from available choices
                const randomIndex = Math.floor(Math.random() * availableChoices.length);
                specifiedColor = availableChoices[randomIndex];
                lastUsedColor = specifiedColor;
            }

            // 

            // Determine shape
            const shape = getShape(material, dateRange);

            // Base properties for all material live events
            const baseEvent = {
                id: `${material._id}`,
                name: dateIndex === 0 ? material.name_text || "" : "", // Only first item has name
                start: dateToUTCTimestamp(dateRange[0]), // First item is start date
                task_type: "material_live_event",
                y: materialIndex, // Use the sorted index position
                borderWidth: 0.5,
                borderColor: shape === 'block' ? specifiedColor?.toLowerCase() : 'none',
                is_current_event: false,
                shape: shape,
                color: specifiedColor?.toLowerCase() || 'grey',
                milestone: false // Always false, we'll use HTML for shapes
            };

            // Add end date for block shapes
            if (shape === 'block') {
                baseEvent.end = dateToUTCTimestamp(dateRange[1]);
                baseEvent.completed = {
                    amount: 1,
                    fill: specifiedColor || 'grey'
                };
                baseEvent.borderRadius = 0; // Square corners for blocks
            }

            return baseEvent;
        }).filter(Boolean); // Filter out any null entries
    }


    function extractMaterialLiveEventsFromDeliverables(deliverables) {
        if (!deliverables || !Array.isArray(deliverables) || deliverables.length === 0) {
            return [];
        }

        const allMaterials = [];
        const materialIdMap = new Map();

        function determinePastelColor(material, index, allMaterials, pastelColorMap, availableColors) {
            const colorKey = (material.colour || '').trim().toLowerCase();

            if (pastelColorMap[colorKey]) {
                return pastelColorMap[colorKey]; // Use valid specified color
            }

            // Build used color set
            const usedColors = new Set(allMaterials.map(m => (m.colour || '').trim().toLowerCase()).filter(c => pastelColorMap[c]));

            // Try unused colors
            const unusedColors = availableColors.filter(c => !usedColors.has(c));
            if (unusedColors.length > 0) {
                const randomUnused = unusedColors[Math.floor(Math.random() * unusedColors.length)];
                material.colour = randomUnused; // Persist fallback
                return pastelColorMap[randomUnused];
            }

            // Try to avoid same as neighbors
            const prev = allMaterials[index - 1];
            const next = allMaterials[index + 1];
            const neighborColors = new Set([
                (prev?.colour || '').trim().toLowerCase(),
                (next?.colour || '').trim().toLowerCase()
            ]);

            const filtered = availableColors.filter(c => !neighborColors.has(c));
            const fallback = filtered.length > 0 ? filtered[Math.floor(Math.random() * filtered.length)] : availableColors[Math.floor(Math.random() * availableColors.length)];
            material.colour = fallback;
            return pastelColorMap[fallback];
        }


        deliverables.forEach(deliverable => {
            if (deliverable.matertials_list_custom_material &&
                Array.isArray(deliverable.matertials_list_custom_material)) {

                deliverable.matertials_list_custom_material.forEach(material => {
                    if (!material) return;

                    if (!materialIdMap.has(material._id)) {
                        allMaterials.push(material);
                        materialIdMap.set(material._id, allMaterials.length - 1);
                    }
                });
            }
        });


        allMaterials.sort((a, b) => {
            if (!a.liveDates?.length || !b.liveDates?.length) {
                return 0;
            }

            const aFirstRange = a.liveDates[0];
            const bFirstRange = b.liveDates[0];

            if (!Array.isArray(aFirstRange) || !Array.isArray(bFirstRange) ||
                aFirstRange.length < 1 || bFirstRange.length < 1) {
                return 0;
            }

            const aStartDate = new Date(aFirstRange[0]).getTime();
            const bStartDate = new Date(bFirstRange[0]).getTime();

            if (aStartDate !== bStartDate) {
                return aStartDate - bStartDate;
            }

            return (a.name_text || '').localeCompare(b.name_text || '');
        });

        allMaterials.forEach((material, index) => {
            materialIdMap.set(material._id, index);
        });

        // Define pastel color mapping
        const pastelColorMap = {
            grey: '#D3D3D3',
            brown: '#E6D3B3',
            red: '#FFB6C1',
            yellow: '#FFFACD',
            orange: '#FFDAB9',
            pink: '#FFDEE9',
            purple: '#E6E6FA',
            green: '#C1F0DC',
            blue: '#ADD8E6',
            black: '#B0B0B0' // Soft grey as pastel black
        };

        const availableColors = Object.keys(pastelColorMap);
        let lastUsedColor = null;

        const materialEvents = allMaterials.flatMap((material, materialIndex) => {
            if (!material || !material.liveDates || !Array.isArray(material.liveDates) || material.liveDates.length === 0) {
                return [];
            }

            let specifiedColor = material.colour?.toLowerCase();

            //TODO If pastelColorMap[''] is undefined, it should use this rule to determine the color
            const pastelColor = determinePastelColor(material, materialIndex, allMaterials, pastelColorMap, availableColors);


            return material.liveDates.map((dateRange, dateIndex) => {
                if (!Array.isArray(dateRange) || dateRange.length < 2) {
                    return null;
                }

                const shape = getShape(material, dateRange);
                const isBlock = shape === 'block';
                const baseEvent = {
                    id: `${material._id}`,
                    name: dateIndex === 0 ? material.name_text || "" : "",
                    start: dateToUTCTimestamp(dateRange[0]),
                    completed: {
                        amount: 1,
                        fill: pastelColor
                    },
                    task_type: "material_live_event",
                    y: materialIdMap.get(material._id),
                    borderWidth: 0.5,
                    borderRadius: '0%',
                    color: pastelColor,
                    borderColor: isBlock ? pastelColor : 'none',
                    is_current_event: false,
                    shape: shape
                };

                return {
                    ...baseEvent,
                    end: dateToUTCTimestamp(dateRange[1]),
                    milestone: false
                };
            }).filter(Boolean);
        });

        return materialEvents;
    }

    function getShape(material, dateRange) {
        // Convert dates to Date objects for comparison
        const startDate = new Date(dateRange[0]);
        const endDate = new Date(dateRange[1]);

        // Calculate the difference in milliseconds
        const timeDiff = endDate - startDate;
        const hoursDiff = timeDiff / (1000 * 60 * 60);

        // Determine shape based on conditions
        if (hoursDiff <= 24 && material.repeat === false) {
            return 'triangle';
        } else if (hoursDiff > 24) {
            return 'block';
        } else if (hoursDiff <= 24 && material.repeat === true) {
            return 'circle';
        } else {
            return 'block';
        }
    }


    /**
    * Takes the result from fetchActiveActivitiesData and returns a list of activity event series objects.
    * @param {Array} activities - The list of activities.
    * @returns {Array} The list of activity event series objects.
    */
    function extractTimeSeriesFromFetchedActivitiesJSON(fetchActiveActivitiesDataResult, currentEvent = null) {
        const activities = processActivitiesSuccessorDeps(fetchActiveActivitiesDataResult);  // Assuming this fetches the activity data
        let currentEventID = currentEvent
        let activityEventSeries = activities.map((activity, index) => {
            const { activityTimeline, launchMilestone } = generateTimeSeries(activity, index, currentEventID);

            return {
                name: `${activity.name_text + 1} ${index + 1}`,
                id: activity._id,
                data: [activityTimeline, launchMilestone]  // Includes both the main activity and its "launch" milestone
            };
        });
        return activityEventSeries;
    }

    function renderTimeline(newDataSeries, chartID) {
        if (document.getElementById(chartID)) {
            options.series = newDataSeries;


            let parentElement = $(`#${chartID}`).parent()
            const calculatedHeight = parentElement.hasClass('defaultMode')
                ? parentElement.outerHeight() - 60
                : parentElement.outerHeight();

            const newOptions = setUpChartOption({ events: newDataSeries, height: calculatedHeight, max: 5 });
            Highcharts.ganttChart(`${chartID}`, newOptions, function (chart) {
                initializeAuxiliaryTimelineFeatures(chart);
                adjustEventDates(newDataSeries, chartID);
            });
            showTimelineLoader(chartID)
            var currentTimeWithMonth = new Date().toLocaleString('en-GB', { hour: '2-digit', minute: '2-digit', second: '2-digit' }).replace(',', '');
            console.debug("renderTimeline called at:", currentTimeWithMonth);
        }

    }

    function renderTimelineWithOption(newDataSeries, chartID, configOptions) {
        if (document.getElementById(chartID)) {
            configOptions.series = newDataSeries

            Highcharts.ganttChart(`${chartID}`, configOptions, function (chart) {
                initializeAuxiliaryTimelineFeatures(chart);
            });

            showTimelineLoader(chartID)
        }

    }


    function showTimelineLoader(chartID, state) {
        if (state) {
            $('#' + chartID).siblings('.loading-overlay').addClass('active');
        } else {
            $('#' + chartID).siblings('.loading-overlay').removeClass('active');
        }
    }

    function deriveTargetDeps(inputList, eventId) {
        /**
         * Extracts the lists of 'target_id' and 'source_id' values from the given list of dependency objects,
         * filtering by type 'successor' and excluding the specified eventId.
         * @param {Array} inputList - A list of dependency objects.
         * @param {string} eventId - An event ID to exclude from the lists.
         * @returns {Object} - An object containing 'target_ids' and 'source_ids', where type is 'successor'.
         */
        if (!inputList) {
            return { target_ids: [], source_ids: [] };
        }

        const target_ids = inputList
            .filter(item => item.type === 'successor') // Ensure the item type is 'successor'
            .map(item => item.target_id) // Extract 'target_id'
            .filter(id => id && id !== eventId); // Remove undefined/null/false values and exclude eventId

        const source_ids = inputList
            .filter(item => item.type === 'successor') // Ensure the item type is 'successor'
            .map(item => item.source_id) // Extract 'source_id'
            .filter(id => id && id !== eventId); // Remove undefined/null/false values and exclude eventId

        return { target_ids, source_ids };
    }

    function deriveAssigns(obj, type) {
        const unique = (array) => [...new Set(array)]; // Utility to ensure uniqueness
        const formatAssignments = (ids) =>
            ids.map((id) => ({
                name: id,
                icon: image_assets.avatar1_icon,
            }));

        if (type === 'activity') {
            // Combine authorisers from both activity and deliverable levels, ensuring uniqueness
            const authorisers = unique([
                ...(obj.authorisers || []),
                ...(obj.deliverable_authorisers || []),
            ]);
            // Use contributors from deliverable level
            const contributors = unique(obj.deliverable_contributors || []);

            return {
                authorisers: formatAssignments(authorisers),
                contributors: formatAssignments(contributors),
            };
        } else if (type === 'deliverable') {
            // Use only the deliverable-specific authorisers and contributors
            const authorisers = unique(obj.authorisers || []);
            const contributors = unique(obj.contributors || []);

            return {
                authorisers: formatAssignments(authorisers),
                contributors: formatAssignments(contributors),
            };
        }

        // Return empty object if type is neither 'activity' nor 'deliverable'
        return {
            authorisers: [],
            contributors: [],
        };
    }

    function calculateWorkdays(givenDate, totalDays, backwards = true) {
        let currentDate = new Date(givenDate);

        // Check if totalDays is 0
        if (totalDays === 0) {
            // If the currentDate is Saturday (6) or Sunday (0)
            if (currentDate.getDay() === 6) { // Saturday
                currentDate.setDate(currentDate.getDate() - 1); // Move to Friday
            } else if (currentDate.getDay() === 0) { // Sunday
                currentDate.setDate(currentDate.getDate() - 2); // Move to Friday
            }
            return currentDate; // Return the closest past weekday
        }

        // Loop to adjust the date by skipping weekends
        while (totalDays > 0) {
            currentDate.setDate(currentDate.getDate() + (backwards ? -1 : 1));
            // Skip weekends (Saturday: 6, Sunday: 0)
            if (currentDate.getDay() !== 0 && currentDate.getDay() !== 6) {
                totalDays -= 1;
            }
        }

        return currentDate;
    }

    // Function to get dependencies where the target is in the deliverables list and is_dependent_into is true
    function deriveRelated(dependencies, siblings) {
        const deliverableIds = (siblings || []).map((d) => d._id);
        return (dependencies || [])
            .filter(
                (dependency) =>
                    deliverableIds.includes(dependency.target_id) && !dependency.is_dependent_into
            )
            .map((dependency) => dependency.target_id);
    }

    // get deliverables predecessor deps
    function getInternalDependencies(input) {
        if (!Array.isArray(input)) {
            return []; // Return an empty array if input is null, undefined, or not an array
        }

        return input
            .filter(item => item.type === 'predecessor' && item.is_dependent_into === false && item.data_type === "deliverable")
            .map(item => item.source_id);
    }

    // get deliverables predecessor deps
    function getExternalDependencies(input) {
        if (!Array.isArray(input)) {
            return []; // Return an empty array if input is null, undefined, or not an array
        }

        return input
            .filter(item => item.type === 'predecessor' && item.is_dependent_into === true && item.data_type === "activity")
            .map(item => `Id${item.source_id}`); // Ensure this is the correct ID property
    }

    // Initialized a default resource
    const allResources = [];
    function deriveReservation(activity) {
        if (!activity) {
            throw new Error("Activity object is required");
        }

        // Parse the live_date and handle the date c   format
        // const [day, month, year] = activity.live_date.split("/");
        const liveDate = convertToDate(activity.start_date_date)

        if (isNaN(liveDate.getTime())) {
            throw new Error("Invalid live_date format");
        }

        // Calculate the completedDate based on the live_date and approval_time
        const approvalTimeInMilliseconds = activity.approval_time * 24 * 60 * 60 * 1000;
        // const completedDate = new Date(liveDate.getTime() - approvalTimeInMilliseconds).toISOString();
        const activityLatestWorkEndDate = new Date(liveDate.getTime() - (activity.approval_time_number || 0) * 24 * 60 * 60 * 1000);

        // Create unique id to enforce deliverable are schedule in sequnce
        const randomString = Math.random().toString(36).substring(2, 10);


        // Helper function to map priority_option to priority value
        function getPriorityValue(priorityOption) {
            switch (priorityOption) {
                case 'p1': return 100;
                case 'p2': return 80;
                case 'p3': return 60;
                case 'p4': return 40;
                case 'p5': return 20;
                default: return 50; // Default priority if undefined
            }
        }

        function convertToDate(dateString) {
            const [day, month, year] = dateString.split('/');
            return new Date(`${year}-${month}-${day}`);
        }




        // Function to build the resources of a deliverable
        function getDeliverableResources(deliverable) {
            var copy_deliverable = deliverable
            if (deliverable.is_parallel) {
                // If deliverable is parallel, wrap contributors in a double bracket
                // copy_deliverable.contributors.push(randomString);
                // return [copy_deliverable.contributors];

                // Temporary workaround: enables parallel execution of deliverables by instantiating a new resource
                var rand = Math.random().toString(36).substring(2, 10);
                allResources.push(rand);
                return rand
            } else {
                // If deliverable is not parallel, return contributors in a single bracket
                // copy_deliverable.contributors.push(randomString)
                // return copy_deliverable.contributors;

                // Temporary solution: using a constant resource enforces sequential scheduling for deliverables by default
                return randomString
            }
        }

        // Function to extract unique contributors and map them to [{id: "A"}] format
        function getUniqueContributors(activityObject) {
            // Step 1: Ensure deliverables_list exists and has items, otherwise return an empty array
            if (!activityObject.deliverables_list_custom_deliverable2 || activityObject.deliverables_list_custom_deliverable2.length === 0) {
                return []; // No deliverables, return empty result
            }

            // Step 2: Flatten the list of contributors from all deliverables, accounting for empty contributors lists
            // const allContributors = activityObject.deliverables_list.flatMap(deliverable =>
            //     deliverable.contributors && deliverable.contributors.length > 0 ? deliverable.contributors : [{id: 'R1'}]
            // );

            // Step 3: Create a Set to ensure unique contributors
            // const uniqueContributors = [...new Set(allContributors)];
            // Temp fix
            const uniqueContributors = allResources;
            uniqueContributors.push(randomString);

            // Step 4: Map the unique contributors to the desired format [{id: "A"}]
            return uniqueContributors.map(contributor => ({ id: contributor }));
        }

        // Extract the activity contributors
        let all_contributors = getUniqueContributors(activity)

        // Extract the deliverables and format them for the reservation object

        const deliverables = (activity.deliverables_list_custom_deliverable2 || []).map((deliverable) => ({
            id: deliverable._id,
            duration: deliverable.duration,
            dependsOn: deriveRelated(deliverable.dependencies_list_custom_dependency, activity.deliverables_list_custom_deliverable2),
            priority: getPriorityValue(deliverable.priority_option),
            resources: getDeliverableResources(deliverable)
        }));

        // Construct the reservation object
        const reservation = {
            completedDate: activityLatestWorkEndDate,
            deliverables: deliverables,
            resources: all_contributors
        };
        return reservation;
    }

    function buildScheduleObject(deliverables) {
        var t = schedule.tasks()
            .id(function (d) { return d.id; })
            .duration(function (d) {
                return (d.duration * 1440);
            })
            .priority(function (d) {
                return d.priority;
            })
            .minSchedule(function (d) {
                return (d.duration * 1440);
            })
            .dependsOn(function (d) {
                return d.dependsOn ? d.dependsOn : undefined;
            })
            .resources(function (d) {
                return d.resources ? d.resources : ['R1'];
            })
        //  assume deliverable are done by diff contributors
        // .resources(function (d) {
        //   return d.resources ? d.resources : ['E1'];
        // })
        return t(deliverables)

    }

    function getTotalDuration(scheduleObj) {
        if (scheduleObj.success) {
            return Math.ceil((scheduleObj.end - scheduleObj.start) / (1000 * 60 * 60 * 24));
        } else {
            throw new Error("Auto scheduling failed!");
        }
    }

    // TODO Remove deprecated function
    function autoScheduling(activityObject) {
        // initialize task list
        var schedulesBuild = buildScheduleObject(activityObject.deliverables),
            completedDate = new Date(activityObject.completedDate),
            initialSchedule = schedule.create(schedulesBuild, activityObject.resources, null, completedDate);

        // Get duration length for schedule
        var tt = getTotalDuration(initialSchedule)
        // Backdate schedule
        var it = new Date(completedDate)
        var backDate = new Date(it.setDate(it.getDate() - tt))

        // new backdate schedule
        var backwardSchedules = schedule.create(schedulesBuild, activityObject.resources, null, backDate);

        return backwardSchedules
        // updateDataWithNewInfo(ns)
    }

    async function updateSchedule(activity_id, deliverables) {
        // Helper function to make a POST request
        const postRequest = async (url, data) => {
            try {
                const response = await fetch(url, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify(data)
                });
                return response.ok;
            } catch (error) {
                console.error(`Failed to update data at ${url}:`, error);
                return false;
            }
        };

        const activityTimes = getDateRange(deliverables);
        // Update the activity start and end times
        const updateActivityData = {
            id: activity_id,
            workStartDate: activityTimes.start,
            workEndDate: activityTimes.end
        };

        const activityUpdated = await postRequest(updateActivityEndpoint, updateActivityData);
        if (!activityUpdated) {
            console.error(`Failed to update activity with ID ${activity_id}`);
            return false;
        }

        // Update deliverables concurrently
        await Promise.all(deliverables.map(async task => {
            const updateDeliverableData = {
                id: task._id,
                start_date: task.start,
                end_date: task.end,
                position: task.position
            };
            const deliverableUpdated = await postRequest(updateDeliverableEndpoint, updateDeliverableData);
            if (!deliverableUpdated) {
                console.error(`Failed to update deliverable with ID ${task._id}`);
            }
        }));

        console.debug("Schedule updates completed successfully");
        return true;
    }

    //return an object with activity timeline with milestone
    async function generateActivityTimeseries(activity_id, currentEventId) {
        var getA = await fetchActivityDataByID(activity_id);
        const flattedDeliveribles = processTasksSuccessorDeps(getA.deliverables_list_custom_deliverable2)
        getA.deliverables_list_custom_deliverable2 = flattedDeliveribles
        var timeSeries = generateTimeSeries(getA, 0, currentEventId); //build timeseries for chart.
        return timeSeries

    }


    async function renderDeliverablesTimeline(a_id, d_id = null, chartElementID) {
        showTimelineLoader(chartElementID, true);
        let currentEventId = d_id ? { id: d_id, type: 'deliverable' } : null;
        try {
            // Generate activity timeseries based on provided activity and deliverable IDs
            const a_obj = await generateActivityTimeseries(a_id, currentEventId);
            const calculatedHeight = $(`#${chartElementID}`).hasClass('defaultView')
                ? $(`#${chartElementID}`).height() - 66
                : $(`#${chartElementID}`).parent().height();

            // Setup options for the chart
            const newOpts = setUpDeliverableOption(a_obj.activityTimeline.deliverables, { height: calculatedHeight, rangeSelector: false });
            // Prepare chart data
            const chartData = {
                ...newOpts,
                series: [
                    {
                        name: 'Deliverables Timeline',
                        data: a_obj.activityTimeline.deliverables
                    }
                ]
            };

            // Render the Gantt chart
            Highcharts.ganttChart(chartElementID, chartData);
        } catch (error) {
            console.error("Error rendering activity timeline:", error);
        } finally {
            // Hide the loader regardless of success or error
            showTimelineLoader(chartElementID);
        }
    }

    async function renderSetupDeliverablesTimeline(a_id, d_id = null, chartElementID) {
        showTimelineLoader(chartElementID, true);
        let currentEventId = d_id ? { id: d_id, type: 'deliverable' } : null;

        try {
            // Generate activity timeseries based on provided activity and deliverable IDs
            const a_obj = await generateActivityTimeseries(a_id, currentEventId);
            let parentElement = $(`#${chartElementID}`).parent();

            const calculatedHeight = parentElement.hasClass('defaultMode')
                ? parentElement.outerHeight() - 60
                : parentElement.outerHeight();
            // Setup options for the chart
            const newOpts = setUpDeliverableOption(a_obj.activityTimeline.deliverables, { height: calculatedHeight, rangeSelector: true, max: 5 });
            // Prepare chart data
            const chartData = {
                ...newOpts,
                series: [
                    {
                        name: 'Deliverables Timeline',
                        data: a_obj.activityTimeline.deliverables
                    }
                ]
            };

            // Render the Gantt chart

            Highcharts.ganttChart(chartElementID, chartData), function (chart) {
                initializeAuxiliaryTimelineFeatures(chart);
            };
            adjustEventDates(a_obj.activityTimeline.deliverables, chartElementID)
        } catch (error) {
            console.error("Error rendering activity timeline:", error);
        } finally {
            // Hide the loader regardless of success or error
            showTimelineLoader(chartElementID);
        }
    }

    function generateTimelineForActivity(activityObject, currentEventID) {
        const activity = activityObject
        activity.deliverables_list_custom_deliverable2 = processTasksSuccessorDeps(activity.deliverables_list_custom_deliverable2);
        return generateTimeSeries(activity, 0, currentEventID);
    }


    function renderTimelineToDeliverableChart(timeseries, chartElementID, eventType = "deliverable") {
        const parentElement = $(`#${chartElementID}`).parent();
        const timelineSection = parentElement.closest('section.timeline');
        const detailView = timelineSection.find('#detailView');
        const liveEventView = timelineSection.find('#liveEventView');
        const activityOverview = timelineSection.find('#activity_overview');
        const chevronContainer = timelineSection.find('#active_activity_container');
        const subEventNavButton = timelineSection.find('#active_activity');
        const toggleSubEventViews = timelineSection.find('#toggle-sub-event-views');
        const toggleDeliverableButton = toggleSubEventViews.find('#toggle_deliverable_view');
        const toggleLiveButton = toggleSubEventViews.find('#toggle_live_view');
        const deliverableCheckIcon = toggleDeliverableButton.find('span.material-symbols-outlined');
        const liveCheckIcon = toggleLiveButton.find('span.material-symbols-outlined');

        // Ensure subEvents properties are arrays
        const deliverables = timeseries.activityTimeline.deliverables || [];
        const materials = timeseries.activityTimeline.material_live_events || [];

        // Set background class based on eventType
        if (eventType === 'material') {
            timelineSection.addClass('materialView');
        } else {
            timelineSection.removeClass('materialView');
        }

        // Calculate height for charts
        const calculatedHeight = parentElement.hasClass('defaultMode')
            ? parentElement.outerHeight() - 80
            : parentElement.outerHeight();

        let max = 5;

        // Always render deliverables chart, even with empty data
        const deliverableOpts = setUpDeliverableOption(deliverables.length > 0 ? deliverables : [], {
            height: calculatedHeight,
            rangeSelector: true,
            max: max
        });

        const deliverableChartData = {
            ...deliverableOpts,
            series: [{
                name: 'Deliverables Timeline',
                data: deliverables.length > 0 ? deliverables : []
            }]
        };

        // Add empty state message for deliverables
        if (deliverables.length === 0) {
            deliverableOpts.chart.events = {
                load: function () {
                    this.renderer.text('No deliverables available for this activity',
                        this.plotWidth / 2 + this.plotLeft - 100,
                        this.plotHeight / 2 + this.plotTop
                    )
                        .css({
                            color: '#666',
                            fontSize: '14px'
                        })
                        .add();
                }
            };
        }

        Highcharts.ganttChart(detailView[0], deliverableChartData, function () {
            if (deliverables.length > 0) {
                const firstEvent = deliverables.at(-1);
                const FOUR_DAYS_IN_MS = 3 * 24 * 60 * 60 * 1000;
                const TWENTY_SEVEN_DAYS_IN_MS = 27 * 24 * 60 * 60 * 1000;

                const adjustedStart = firstEvent.start - FOUR_DAYS_IN_MS;
                const adjustedEnd = firstEvent.end + TWENTY_SEVEN_DAYS_IN_MS;
                const chart = (Highcharts.charts || [])
                    .filter(chart => chart)
                    .find(chart => chart.renderTo === detailView[0]);

                chart.xAxis[0].setExtremes(adjustedStart, adjustedEnd);
            }
        });

        // Always render materials chart, even with empty data
        const materialOpts = setUpDeliverableOption(materials.length > 0 ? materials : [], {
            height: calculatedHeight,
            rangeSelector: true,
            max: max
        });

        // Apply material-specific chart styling
        materialOpts.chart.backgroundColor = '#EAFAF3';
        materialOpts.plotOptions.series.borderRadius = '0%';

        // Add empty state message for materials
        if (materials.length === 0) {
            materialOpts.chart.events = {
                load: function () {
                    this.renderer.text('No material live events available for this activity',
                        this.plotWidth / 2 + this.plotLeft - 120,
                        this.plotHeight / 2 + this.plotTop
                    )
                        .css({
                            color: '#666',
                            fontSize: '14px'
                        })
                        .add();
                }
            };
        }

        const materialChartData = {
            ...materialOpts,
            series: [{
                name: 'Material Live Events Timeline',
                data: materials.length > 0 ? materials : []
            }]
        };

        Highcharts.ganttChart(liveEventView[0], materialChartData, function () {
            if (materials.length > 0) {
                const firstEvent = materials.at(-1);
                const FOUR_DAYS_IN_MS = 3 * 24 * 60 * 60 * 1000;
                const TWENTY_SEVEN_DAYS_IN_MS = 27 * 24 * 60 * 60 * 1000;

                const adjustedStart = firstEvent.start - FOUR_DAYS_IN_MS;
                const adjustedEnd = firstEvent.end + TWENTY_SEVEN_DAYS_IN_MS;
                const chart = (Highcharts.charts || [])
                    .filter(chart => chart)
                    .find(chart => chart.renderTo === liveEventView[0]);

                chart.xAxis[0].setExtremes(adjustedStart, adjustedEnd);
            }
        });

        // Update UI elements
        chevronContainer.removeClass('hidden');
        // activityOverview.addClass('disabled');
        // activityOverview.attr('disabled', 'disabled');
        subEventNavButton.text(timeseries.activityTimeline.name || "Activity Details").removeClass('hidden');

        // Update visibility and toggle button states based on eventType
        if (eventType === 'material') {
            detailView.addClass('hidden');
            liveEventView.removeClass('hidden');
            toggleDeliverableButton.removeClass('active');
            toggleLiveButton.addClass('active');
            deliverableCheckIcon.addClass('hidden');
            liveCheckIcon.removeClass('hidden');
        } else {
            detailView.removeClass('hidden');
            liveEventView.addClass('hidden');
            toggleDeliverableButton.addClass('active');
            toggleLiveButton.removeClass('active');
            deliverableCheckIcon.removeClass('hidden');
            liveCheckIcon.addClass('hidden');
        }

        initializeAuxiliaryFeaturesForDeliverableChart(chartElementID, timeseries);

        showTimelineLoader(chartElementID);
    }

    function renderTimelineActivityOverview(timeseries, chartElementID) {
        const parentElement = $(`#${chartElementID}`).parent();
        const calculatedHeight = $(`#${chartElementID}`).hasClass('defaultView')
            ? $(`#${chartElementID}`).height() - 66
            : parentElement.height();

        // Setup options for the chart
        const newOpts = setUpDeliverableOption(timeseries.activityTimeline.deliverables, {
            height: calculatedHeight,
            rangeSelector: false
        });

        const chartData = {
            ...newOpts,
            series: [{
                name: 'Deliverables Timeline',
                data: timeseries.activityTimeline.deliverables
            }]
        };

        // Render the Gantt chart with a callback to set 30-day range
        Highcharts.ganttChart(chartElementID, chartData, function (chart) {
            // Find the earliest start date
            const deliverables = timeseries.activityTimeline.deliverables;
            if (deliverables && deliverables.length > 0) {
                const earliestStart = Math.min(...deliverables.map(d => d.start));
                const thirtyDaysInMs = 30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds
                const endDate = earliestStart + thirtyDaysInMs;

                // Set the x-axis to earliest start + 30 days
                chart.xAxis[0].setExtremes(earliestStart, endDate);
            }
        });

        showTimelineLoader(chartElementID, false);
    }

    async function renderSingleDeliverableTimeline(id, data) {
        const chartID = `deliverableSideSheet${id}`;
        const yAxis = 2;
        const parentElement = $(`#${chartID}`).parent();
        const calculatedHeight = parentElement.outerHeight();

        showTimelineLoader(chartID, true);

        const deliverableTsData = data ? JSON.parse(data) : await generateDeliverableTimeseries(id);
        const { start, end, name } = deliverableTsData;


        const adjustDate = (date, offset) => new Date(new Date(date).getTime() + offset).toISOString();
        const extremeStart = adjustDate(start, -86400000); // subtract one day (86400000 ms)
        const extremeEnd = adjustDate(end, 86400000); // add one day (86400000 ms)

        // Set up the chart options
        Object.assign(deliverableTsData, {
            y: 1,
            borderWidth: 3,
            borderColor: '#3E5F90'
        });

        const newOpts = setUpDeliverableOption([deliverableTsData], { height: calculatedHeight, max: yAxis });

        newOpts.rangeSelector.enabled = false;

        const chartData = {
            ...newOpts,
            series: [
                {
                    name,
                    data: [deliverableTsData]
                }
            ]
        };

        // Render the Highchart
        if (document.getElementById(chartID)) {
            Highcharts.ganttChart(chartID, chartData, function (chart) {
                chart.xAxis[0].setExtremes(extremeStart, extremeEnd);
            });
            showTimelineLoader(chartID, false);
        }
    }

    //TODO setUpDeliverableOption and setUpChartOption should be same fx.
    function setUpDeliverableOption(timeSeries, config) {
        // Find the earliest start time
        const min = findEarliestTimeDeliverables(timeSeries);
        const max = min + 30 * 24 * 60 * 60 * 1000;
        const padding = 2 * 24 * 3600 * 1000;

        // Create a clone of the options object
        const newOptions = $.extend(true, {}, options)

        if (Array.isArray(newOptions.xAxis)) {
            newOptions.xAxis = [...newOptions.xAxis];
            newOptions.xAxis[0] = {
                ...newOptions.xAxis[0],
                min: min - padding, // Update min
                // max: config.xAxis ? config.xAxis.max + padding : undefined
            };
        }

        if (newOptions.rangeSelector && config.rangeSelector) {
            newOptions.rangeSelector = {
                ...newOptions.rangeSelector,
                enabled: config.rangeSelector // Disable range selector
            };
        }


        if (newOptions.chart.height && config.height) {
            newOptions.chart.height = (config.height || 0) + 10;
        }

        if (newOptions.yAxis.max && config.max) {
            newOptions.yAxis.max = config.max;
        }


        return newOptions;
    }

    function setUpChartOption(eventsConfig) {
        const extremes = findTimeExtremities(eventsConfig.events)
        const min = extremes.min;
        const max = extremes.max;
        const padding = 2 * 24 * 3600 * 1000;
        const newOptions = $.extend(true, {}, options);

        if (Array.isArray(newOptions.xAxis)) {
            newOptions.xAxis = [...newOptions.xAxis];
            newOptions.xAxis[0] = {
                ...newOptions.xAxis[0],
                min: min - padding,
                // max: max + padding
            };
        }

        if (newOptions.rangeSelector && eventsConfig.rangeSelector) {
            newOptions.rangeSelector = {
                ...newOptions.rangeSelector,
                enabled: true // Disable range selector
            };
        }

        if (newOptions.yAxis.max && eventsConfig.max) {
            newOptions.yAxis.max = eventsConfig.max
        }

        if (newOptions.chart.height && eventsConfig.height) {
            newOptions.chart.height = eventsConfig.height;
        }

        return newOptions;

    }

    // Helper function to find the earliest start time in deliverables
    function findEarliestTimeDeliverables(events) {
        return events.reduce((earliest, event) => {
            return event.start < earliest ? event.start : earliest;
        }, Infinity); // Start with a very high value
    }

    function findTimeExtremities(timeSeries) {
        const allItems = timeSeries.flatMap(obj => obj.data);

        // Extract all start times
        const startTimes = allItems.map(item => item.start).filter(Boolean);

        // Extract all start times
        const endTimes = allItems.map(item => item.end).filter(Boolean);

        // Find the minimum start time
        const earliestStart = Math.min(...startTimes);

        // Find the minimum start time
        const latestEnd = Math.max(...endTimes);

        return { min: earliestStart, max: latestEnd };
    }

    function getDateRange(items) {
        // Handle empty array
        if (!items || !items.length) {
            return {
                start: null,
                end: null
            };
        }

        // Initialize with null to handle invalid dates
        let earliestStart = null;
        let latestEnd = null;

        items.forEach(item => {
            // Skip if item doesn't have start or end dates
            if (!item.start || !item.end) {
                return;
            }

            // Try to create valid date objects
            const startDate = new Date(item.start);
            const endDate = new Date(item.end);

            // Skip if dates are invalid
            if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
                return;
            }

            // If this is our first valid date, use it to initialize
            if (earliestStart === null) {
                earliestStart = startDate;
                latestEnd = endDate;
                return;
            }

            // Compare dates
            if (startDate < earliestStart) {
                earliestStart = startDate;
            }

            if (endDate > latestEnd) {
                latestEnd = endDate;
            }
        });

        return {
            start: earliestStart,
            end: latestEnd
        };
    }

    //Helper Fx for getting replica of a timeseries
    function createTsNewObject(original) {
        return {
            id: original.object?.id,
            name: original.name,
            activity_id: original.activity_id,
            start: new Date(original.start).toISOString(),  // Convert to UTC timestamp
            end: new Date(original.end).toISOString(),      // Convert to UTC timestamp
            dependency: original.dependency,
            completed: original.completed,
            color: original.color,
            owner: original.owner,
            assignees: original.assignees,
            status: original.status,
            has_dependant: original.has_dependant,
            zIndex: original.zIndex,
            task_type: original.task_type,
            dependencies: original.dependencies,
            y: original.y,
            borderWidth: original.borderWidth,
            borderColor: original.borderColor,
            priority: original.priority
        };
    }

    async function renderAllActivityTimeline() {
        //expand later now just do this basic
        var a = await fetchActiveActivitiesData();
        var timeline = extractTimeSeriesFromFetchedActivitiesJSON(a);
        window.localStorage.setItem("timelineSeries", JSON.stringify(timeline));

        renderTimeline(timeline, 'dashboard');
        renderTimeline(timeline, 'cna');

    }

    async function renderCurrentActivityTimeline(activity_id) {

    }

    function renderComponentTimeline(componentID) {
        const activities = JSON.parse(localStorage.fetchedActivities || []);
        const activeTimeSeries = extractTimeSeriesFromFetchedActivitiesJSON(activities);
        renderTimeline(activeTimeSeries, componentID);
    }

    async function renderCompareActivities(activityId, fullScreen = null) {
        showTimelineLoader('cna', true)
        let mergedActivities = await mergeCurrentActivityAndActiveActivities(activityId);

        var currentTimeWithMonth = new Date().toLocaleString('en-GB', { hour: '2-digit', minute: '2-digit', second: '2-digit' }).replace(',', '');

        let currentEventId = { id: activityId, type: 'activity' }
        if (fullScreen) {
            //do some calculation to show the current view
        }
        const newTimeSeries = extractTimeSeriesFromFetchedActivitiesJSON(mergedActivities, currentEventId);
        //setUp option
        renderTimeline(newTimeSeries, 'cna')
    }

    async function mergeCurrentActivityAndActiveActivities(id) {
        let currentActivity = await fetchActivityDataByID(id);
        let fetchedActivities = await fetchActiveActivitiesData();
        fetchedActivities = fetchedActivities ? fetchedActivities : [];

        const updatedfetchedActivities = fetchedActivities.some(item => item._id === currentActivity._id)
            ? fetchedActivities.map(item => item._id === currentActivity._id ? currentActivity : item) // Replace the entire object if found
            : [...fetchedActivities, currentActivity]; // Add to the end if not found
        localStorage.setItem('CnaMergeActivities', JSON.stringify(updatedfetchedActivities))
        return updatedfetchedActivities

    }

    function renderErrorOnTimeline(messages = []) {
        let cnaTimeline = $('.timeline #cna');
        let dTimeline = $('.timeline #deliverable');

        if (cnaTimeline.length === 0 && dTimeline.length === 0) {
            console.error('Both timelines are missing in the DOM');
            return;
        }

        if (messages.length === 0) {
            messages = ['Error: Something went wrong.', 'Please check the timeline.'];
        }

        const messagesList = messages.map(message => `<li>${message}</li>`).join('');

        const insertMessages = (timelineElement) => {
            if (timelineElement.length > 0) {
                const parentElement = timelineElement.parent();
                parentElement.addClass('error');

                let errorList = parentElement.find('ul.error_messages');
                if (errorList.length === 0) {
                    errorList = $('<ul class="error_messages"></ul>');
                    parentElement.append(errorList);
                }

                errorList.html(messagesList);
            }
        };

        insertMessages(cnaTimeline);
        insertMessages(dTimeline);
    }

    function clearErrorOnTimeline() {
        let cnaTimeline = $('.timeline #cna');
        let dTimeline = $('.timeline #deliverable');

        const clearMessages = (timelineElement) => {
            if (timelineElement.length > 0) {
                const parentElement = timelineElement.parent();
                parentElement.removeClass('error'); // Remove the error class
                parentElement.find('ul.error_message').remove(); // Remove the error message list
            }
        };

        clearMessages(cnaTimeline);
        clearMessages(dTimeline);
    }

    async function processFeed(id, type) {
        try {

            !type ? showTimelineLoader('cna', true) : showTimelineLoader('deliverable', true);

            let activityfeed = await fetchActivityDataByID(id);
            const scheduler = new ReverseSchedulingAlgorithm;
            const schedule = scheduler.execute(activityfeed)
            if (schedule.success) {

                clearErrorOnTimeline();
                //wait for update activity schedule to complete running
                var updatedSchedule = await updateSchedule(id, schedule.schedules)
                //update the activity
                if (updatedSchedule) {
                    //Rerender deliverable timeline
                    if (type) {
                        renderSetupDeliverablesTimeline(id, type, 'deliverable');
                    } else {
                        //Re render cna timeline
                        renderCompareActivities(id);
                    }
                };
            } else {
                renderErrorOnTimeline([schedule.error])
            }

        } catch (error) {
            renderErrorOnTimeline()
        }

    }

    //findCurrentEventStart or return the last Start
    function findCurrentEventStart(list) {
        const parent = list.find(obj => obj.data.some(event => event.is_current_event));
        if (parent) {
            const currentEvent = parent.data.find(event => event.is_current_event);

            return currentEvent.start;
        } else {
            const lastEvent = list.at(-1);
            const start = lastEvent.data[0].start
            return start;
        }
    }

    function findCurrentSubEventStart(list) {
        const currentEvent = list.find(event => event.is_current_event);
        return currentEvent ? currentEvent.start : null;
    }

    function adjustEventDates(list, chartId) {
        if (!(Array.isArray(list) && list.length > 0)) {
            return null;
        }

        const start = chartId.includes('deliverable') ? findCurrentSubEventStart(list) : findCurrentEventStart(list);

        if (!start) {
            return null;
        }

        const chartInstance = Highcharts.charts.filter(c => c !== undefined).find(c => c.renderTo.id === chartId);
        // Define time adjustments
        const FOUR_DAYS_IN_MS = 3 * 24 * 60 * 60 * 1000;
        const TWENTY_SEVEN_DAYS_IN_MS = 27 * 24 * 60 * 60 * 1000;

        // Adjust the `start` by subtracting 4 days and adding `end` by 27 to make a month
        const adjustedStart = start - FOUR_DAYS_IN_MS;
        const adjustedEnd = adjustedStart + TWENTY_SEVEN_DAYS_IN_MS;
        chartInstance.xAxis[0].setExtremes(adjustedStart, adjustedEnd)

    }


    function processTasksSuccessorDeps(deliverables) {
        function cloneObject(obj) {
            return JSON.parse(JSON.stringify(obj));
        }

        // Handle empty or invalid deliverables input
        if (!Array.isArray(deliverables) || deliverables.length === 0) {
            return [];
        }

        deliverables.forEach((deliverable) => {
            const updatedDependencies = [];

            // Check if dependencies_list_custom_dependency exists and is an array
            if (!deliverable?.dependencies_list_custom_dependency?.length) {
                deliverable.dependencies_list_custom_dependency = [];
                return;
            }

            deliverable.dependencies_list_custom_dependency.forEach((dependency) => {
                if (
                    dependency?.type === "successor" &&
                    dependency.is_dependent_into === false &&
                    dependency.data_type === "deliverable"
                ) {
                    const sourceDeliverable = deliverables.find(
                        (d) => d?._id === dependency.target_id
                    );

                    if (sourceDeliverable) {
                        const clonedDependency = cloneObject(dependency);

                        clonedDependency.source_id = dependency.source_id;
                        clonedDependency.target_id = dependency.target_id;
                        clonedDependency.type = "predecessor";
                        clonedDependency.obj = deliverable;

                        // Ensure the target array exists
                        if (!Array.isArray(sourceDeliverable.dependencies_list_custom_dependency)) {
                            sourceDeliverable.dependencies_list_custom_dependency = [];
                        }
                        sourceDeliverable.dependencies_list_custom_dependency.push(
                            clonedDependency
                        );
                    }
                } else {
                    updatedDependencies.push(dependency);
                }
            });

            deliverable.dependencies_list_custom_dependency = updatedDependencies;
        });

        return deliverables;
    }
    function processActivitiesSuccessorDeps(activities) {
        function cloneObject(obj) {
            return JSON.parse(JSON.stringify(obj));
        }


        // Handle empty or invalid activities input
        if (!Array.isArray(activities) || activities.length === 0) {
            return [];
        }

        activities.forEach((activity) => {
            const updatedDependencies = [];

            // Ensure dependencies exists
            if (!Array.isArray(activity?.dependencies)) {
                activity.dependencies = [];
            }

            // Process deliverables if they exist
            activity.deliverables_list_custom_deliverable2 = Array.isArray(activity?.deliverables_list_custom_deliverable2)
                ? processTasksSuccessorDeps(activity.deliverables_list_custom_deliverable2)
                : [];

            activity.dependencies.forEach((dependency) => {
                if (
                    dependency?.type === "successor" &&
                    dependency.is_dependent_into === true &&
                    dependency.data_type === "activity"
                ) {
                    const sourceDeliverable = activities.find(
                        (a) => a?._id === dependency.target_id
                    );

                    if (sourceDeliverable) {
                        const clonedDependency = cloneObject(dependency);

                        clonedDependency.source_id = dependency.source_id;
                        clonedDependency.target_id = dependency.target_id;
                        clonedDependency.type = "predecessor";
                        clonedDependency.obj = activity;

                        // Ensure the target array exists
                        if (!Array.isArray(sourceDeliverable.dependencies)) {
                            sourceDeliverable.dependencies = [];
                        }
                        sourceDeliverable.dependencies.push(
                            clonedDependency
                        );
                    }
                } else {
                    updatedDependencies.push(dependency);
                }
            });

            activity.dependencies = updatedDependencies;
        });

        return activities;
    }

    function generateMaterialLiveDates(params) {
        // Validate required parameters
        const requiredParams = ['activityStartDate', 'activityEndDate', 'type', 'duration'];
        for (const param of requiredParams) {
            if (!params[param]) {
                throw new Error(`Missing required parameter: ${param}`);
            }
        }

        // Parse dates and duration
        const { DateTime } = luxon;
        const activityStart = DateTime.fromISO(params.activityStartDate);
        const activityEnd = DateTime.fromISO(params.activityEndDate.replace(/'/g, ''));
        const duration = parseInt(params.duration);

        // Initialize result
        const result = {
            liveDates: [],
            hasExceedingDates: false,
            exceededDates: []
        };

        // Calculate initial start date based on type
        let startDate;
        if (params.type === 'start_with_activity') {
            startDate = activityStart;
        } else if (params.type === 'start_after_activity') {
            if (!params.period_number || !params.period_type) {
                throw new Error('Missing period_number or period_type for start_after_activity');
            }

            const periodNumber = parseInt(params.period_number);
            const periodType = params.period_type;

            // Handle different period types
            if (['day', 'week', 'month', 'year'].includes(periodType)) {
                // For standard time periods, add the number to the activity start date
                const periodTypePlural = periodType.endsWith('s') ? periodType : `${periodType}s`;
                startDate = activityStart.plus({ [periodTypePlural]: periodNumber });
            } else if (['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].includes(periodType)) {
                // For weekdays, find the nth occurrence of that weekday after the activity start
                const weekdayMap = {
                    'monday': 1,
                    'tuesday': 2,
                    'wednesday': 3,
                    'thursday': 4,
                    'friday': 5,
                    'saturday': 6,
                    'sunday': 7
                };

                const targetWeekday = weekdayMap[periodType];
                let currentDate = activityStart;
                let occurrenceCount = 0;

                // Find the first occurrence of the weekday
                while (currentDate.weekday !== targetWeekday) {
                    currentDate = currentDate.plus({ days: 1 });
                }

                occurrenceCount++;

                // Find the nth occurrence
                while (occurrenceCount < periodNumber) {
                    currentDate = currentDate.plus({ weeks: 1 });
                    occurrenceCount++;
                }

                startDate = currentDate;
            } else {
                throw new Error(`Invalid period type: ${periodType}`);
            }
        } else {
            throw new Error(`Invalid type: ${params.type}`);
        }

        // Handle repetition
        const dates = [];
        if (params.repeat === 'yes') {
            const repeatType = params.repeat_type || 'weekly';
            const repeatFrequency = parseInt(params.repeat_frequency || '1');

            if (repeatType === 'yearly') {
                // Handle yearly repetition manually since RRule might not handle it as expected
                let currentYear = startDate.year;
                let currentDate = startDate;

                while (currentDate <= activityEnd) {
                    // Add the current date range
                    const end = currentDate.plus({ days: duration - 1 });
                    dates.push({ start: currentDate, end });

                    // Move to the next year based on frequency
                    currentYear += repeatFrequency;
                    currentDate = currentDate.set({ year: currentYear });
                }
            } else {
                // For other repetition types, use RRule
                const RRule = window.RRule;
                const frequencyMap = {
                    'daily': RRule.DAILY,
                    'weekly': RRule.WEEKLY,
                    'monthly': RRule.MONTHLY
                };

                // Create rule for repetition
                const rule = new RRule({
                    freq: frequencyMap[repeatType] || RRule.WEEKLY,
                    interval: repeatFrequency,
                    dtstart: startDate.toJSDate(),
                    until: activityEnd.toJSDate()
                });

                // Generate all start dates
                const startDates = rule.all();

                // Create date ranges
                for (const date of startDates) {
                    const start = DateTime.fromJSDate(date);
                    const end = start.plus({ days: duration - 1 });
                    dates.push({ start, end });
                }
            }
        } else {
            // Just one date range
            const end = startDate.plus({ days: duration - 1 });
            dates.push({ start: startDate, end });
        }

        // Process date ranges and check for exceeding dates
        for (const { start, end } of dates) {
            const dateRange = {
                start: start.toISODate(),
                end: end.toISODate()
            };

            const dateArray = [new Date(start.toJSDate()), new Date(end.toJSDate())];
            if (end > activityEnd) {
                result.hasExceedingDates = true;
                result.exceededDates.push(dateRange);
            } else {
                result.liveDates.push(dateArray);
            }
        }

        return result;
    }

</script>