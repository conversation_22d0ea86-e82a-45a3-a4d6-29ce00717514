//RENDER SUBEVENTS - use to render subevents of a timeline, eg: if overview series has a,b,c user can click on a and gets subevents of a[1,2,3]
function renderSubEvents(chart, superEventName, subEvents) {
    // Use getTimelineElements to get elements relative to chartElement
    const chartContainer = $(`#${chart.renderTo.id}`)[0].parentNode,
        eventChart = $(`#${chart.renderTo.id}`),
        subEventChart = $(chartContainer).find('#detailView'),
        toggleFullscreenButton = $(chartContainer).find('#toggle-fullscreen'),
        eventNavButton = $(chartContainer).find('#activity_overview'),
        subEventNavButton = $(chartContainer).find('#active_activity');

    if (!subEventChart.is(":visible") && subEvents.length > 0) {
        let parentElement = $(`#${chart.renderTo.id}`).parent()
        let calculatedHeight = parentElement.hasClass('defaultMode')
            ? parentElement.outerHeight() - 80
            : parentElement.outerHeight();




        let max = chart.renderTo.id === 'fullChart' ? 20 : 5;
        // Setup options for the chart
        const newOpts = setUpDeliverableOption(subEvents, { height: calculatedHeight, rangeSelector: true, max: max });
        // Prepare chart data
        const chartData = {
            ...newOpts,
            series: [
                {
                    name: 'Deliverables Timeline',
                    data: subEvents
                }
            ]
        };



        Highcharts.ganttChart(subEventChart[0], chartData, function () {
            //get the chart instance
            const firstEvent = subEvents.at(-1)
            const FOUR_DAYS_IN_MS = 3 * 24 * 60 * 60 * 1000;
            const TWENTY_SEVEN_DAYS_IN_MS = 27 * 24 * 60 * 60 * 1000;

            // Adjust the `start` by subtracting 4 days and adding `end` by 27 to make a month
            const adjustedStart = firstEvent.start - FOUR_DAYS_IN_MS;
            const adjustedEnd = firstEvent.end + TWENTY_SEVEN_DAYS_IN_MS;
            var chart = (Highcharts.charts || [])
                .filter(chart => chart) // Ensures no null/undefined values
                .find(chart => chart.renderTo === subEventChart[0]);

            chart.xAxis[0].setExtremes(adjustedStart, adjustedEnd)
        });

        subEventNavButton.text(superEventName).removeClass('hidden')
        subEventChart.removeClass('hidden');
        eventChart.addClass('hidden');
        subEventNavButton.text(superEventName).removeClass('hidden')
        subEventChart.removeClass('hidden');
        eventChart.addClass('hidden');
    }
}


function renderTimeline(newDataSeries, chartID) {
    if (document.getElementById(chartID)) {
        options.series = newDataSeries;


        let parentElement = $(`#${chartID}`).parent()
        const calculatedHeight = parentElement.hasClass('defaultMode')
            ? parentElement.outerHeight() - 60
            : parentElement.outerHeight();

        const newOptions = setUpChartOption({ events: newDataSeries, height: calculatedHeight, max: 5 });
        Highcharts.charts[0] && Highcharts.chart[0].destroy()
        Highcharts.ganttChart(`${chartID}`, newOptions, function (chart) {
            initializeAuxiliaryTimelineFeatures(chart);
            adjustEventDates(newDataSeries, chartID);
        });
        showTimelineLoader(chartID)
        var currentTimeWithMonth = new Date().toLocaleString('en-GB', { hour: '2-digit', minute: '2-digit', second: '2-digit' }).replace(',', '');
        console.debug("renderTimeline called at:", currentTimeWithMonth);
    }

}

async function renderDeliverablesTimeline(a_id, d_id = null, chartElementID) {
    showTimelineLoader(chartElementID, true);
    let currentEventId = d_id ? { id: d_id, type: 'deliverable' } : null;
    try {
        // Generate activity timeseries based on provided activity and deliverable IDs
        const a_obj = await generateActivityTimeseries(a_id, currentEventId);
        const calculatedHeight = $(`#${chartElementID}`).hasClass('defaultView')
            ? $(`#${chartElementID}`).height() - 66
            : $(`#${chartElementID}`).parent().height();

        // Setup options for the chart
        const newOpts = setUpDeliverableOption(a_obj.activityTimeline.deliverables, { height: calculatedHeight, rangeSelector: false });
        // Prepare chart data
        const chartData = {
            ...newOpts,
            series: [
                {
                    name: 'Deliverables Timeline',
                    data: a_obj.activityTimeline.deliverables
                }
            ]
        };

        // Render the Gantt chart
        Highcharts.ganttChart(chartElementID, chartData);
    } catch (error) {
        console.error("Error rendering activity timeline:", error);
    } finally {
        // Hide the loader regardless of success or error
        showTimelineLoader(chartElementID);
    }
}

function renderTimelineActivityOverview(timeseries, chartElementID) {
    const parentElement = $(`#${chartElementID}`).parent();
    const calculatedHeight = $(`#${chartElementID}`).hasClass('defaultView')
        ? $(`#${chartElementID}`).height() - 66
        : parentElement.height();

    // Setup options for the chart
    const newOpts = setUpDeliverableOption(timeseries.activityTimeline.deliverables, {
        height: calculatedHeight,
        rangeSelector: false
    });

    const chartData = {
        ...newOpts,
        series: [{
            name: 'Deliverables Timeline',
            data: timeseries.activityTimeline.deliverables
        }]
    };

    // Render the Gantt chart with a callback to set 30-day range
    Highcharts.ganttChart(chartElementID, chartData, function (chart) {
        // Find the earliest start date
        const deliverables = timeseries.activityTimeline.deliverables;
        if (deliverables && deliverables.length > 0) {
            const earliestStart = Math.min(...deliverables.map(d => d.start));
            const thirtyDaysInMs = 30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds
            const endDate = earliestStart + thirtyDaysInMs;

            // Set the x-axis to earliest start + 30 days
            chart.xAxis[0].setExtremes(earliestStart, endDate);
        }
    });

    showTimelineLoader(chartElementID, false);
}



async function renderSingleDeliverableTimeline(id, data) {
    const chartID = `deliverableSideSheet${id}`;
    const yAxis = 2;
    const parentElement = $(`#${chartID}`).parent();
    const calculatedHeight = parentElement.outerHeight();

    showTimelineLoader(chartID, true);

    const deliverableTsData = data ? JSON.parse(data) : await generateDeliverableTimeseries(id);
    const { start, end, name } = deliverableTsData;


    const adjustDate = (date, offset) => new Date(new Date(date).getTime() + offset).toISOString();
    const extremeStart = adjustDate(start, -86400000); // subtract one day (86400000 ms)
    const extremeEnd = adjustDate(end, 86400000); // add one day (86400000 ms)

    // Set up the chart options
    Object.assign(deliverableTsData, {
        y: 1,
        borderWidth: 3,
        borderColor: '#3E5F90'
    });

    const newOpts = setUpDeliverableOption([deliverableTsData], { height: calculatedHeight, max: yAxis });

    newOpts.rangeSelector.enabled = false;

    const chartData = {
        ...newOpts,
        series: [
            {
                name,
                data: [deliverableTsData]
            }
        ]
    };

    // Render the Highchart
    if (document.getElementById(chartID)) {
        Highcharts.ganttChart(chartID, chartData, function (chart) {
            chart.xAxis[0].setExtremes(extremeStart, extremeEnd);
        });
        showTimelineLoader(chartID, false);
    }
}


