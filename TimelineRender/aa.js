data = [
    {
        "_id": "1737121635388x242578441137029120",
        "name_text": "Easter Hunt Seasonal Eventss",
        "approval_time_number": 6,
        "start_date_date": "2025-04-16T23:00:00.000Z",
        "end_date_date": "2025-04-30T23:00:00.000Z",
        "activity_creator_user": "1727366821155x669597436804614000",
        "deliverables_list_custom_deliverable2": [
            {
                "_id": "1737126015189x884589256359804900",
                "name_text": "Pre-Launch Coms",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1737126272168x170622201734627330"
                ],
                "contributors": [
                    "1737126272168x170622201734627330"
                ],
                "authorisers": [
                    "1737126274085x185557019097825300"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1737126290773x491430708820574200",
                        "source_id": "1737125818651x160203365718687740",
                        "target_id": "1737126015189x884589256359804900",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737125818651x160203365718687740",
                            "name_text": "Brief Community team",
                            "start": "2025-03-30T00:00:00.000Z",
                            "end": "2025-04-01T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-04-07T23:00:00.000Z",
                "end": "2025-04-14T23:00:00.000Z"
            },
            {
                "_id": "1737125818651x160203365718687740",
                "name_text": "Brief Community team",
                "approval_time_number": 1,
                "time_period_number": 2,
                "duration": 3,
                "user_contributors_list_custom_user_assignment": [
                    "1737125873836x830691410220417000"
                ],
                "contributors": [
                    "1737125873836x830691410220417000"
                ],
                "authorisers": [
                    "1737125875547x508684211797360640"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1737125902862x716363470349795300",
                        "source_id": "1737125135608x675007854869741600",
                        "target_id": "1737125818651x160203365718687740",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737125135608x675007854869741600",
                            "name_text": "UA/Messaging Schedule",
                            "start": "2025-03-25T00:00:00.000Z",
                            "end": "2025-03-29T00:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1737125909021x812023084936069100",
                        "source_id": "1737124776586x878893976938872800",
                        "target_id": "1737125818651x160203365718687740",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737124776586x878893976938872800",
                            "name_text": "Coms  Preparation",
                            "start": "2025-03-20T00:00:00.000Z",
                            "end": "2025-03-24T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-03-30T00:00:00.000Z",
                "end": "2025-04-01T23:00:00.000Z"
            },
            {
                "_id": "1737125697840x882312894914756600",
                "name_text": "Ops Team Briefing",
                "approval_time_number": 1,
                "time_period_number": 2,
                "duration": 3,
                "user_contributors_list_custom_user_assignment": [
                    "1737125752487x510732614100058100"
                ],
                "contributors": [
                    "1737125752487x510732614100058100"
                ],
                "authorisers": [
                    "1737125750446x289300667955675140"
                ],
                "position": 1,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1737125762090x829226163755024400",
                        "source_id": "1737124542866x572806451471581200",
                        "target_id": "1737125697840x882312894914756600",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737124542866x572806451471581200",
                            "name_text": "Deployment to Staging",
                            "start": "2025-03-05T00:00:00.000Z",
                            "end": "2025-03-11T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-03-30T00:00:00.000Z",
                "end": "2025-04-01T23:00:00.000Z"
            },
            {
                "_id": "1737125317556x967383320181080000",
                "name_text": "Deployment to Live",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1737125390523x375378479377809400"
                ],
                "contributors": [
                    "1737125390523x375378479377809400"
                ],
                "authorisers": [
                    "1737125390523x375378479377809400"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1737125402302x626059184934486000",
                        "source_id": "1737124542866x572806451471581200",
                        "target_id": "1737125317556x967383320181080000",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737124542866x572806451471581200",
                            "name_text": "Deployment to Staging",
                            "start": "2025-03-05T00:00:00.000Z",
                            "end": "2025-03-11T00:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1737125991558x864373939217367000",
                        "source_id": "1737125697840x882312894914756600",
                        "target_id": "1737125317556x967383320181080000",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737125697840x882312894914756600",
                            "name_text": "Ops Team Briefing",
                            "start": "2025-03-30T00:00:00.000Z",
                            "end": "2025-04-01T23:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1737125995952x208330780050718720",
                        "source_id": "1737125818651x160203365718687740",
                        "target_id": "1737125317556x967383320181080000",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737125818651x160203365718687740",
                            "name_text": "Brief Community team",
                            "start": "2025-03-30T00:00:00.000Z",
                            "end": "2025-04-01T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": false,
                "is_fixed": false,
                "start": "2025-04-02T23:00:00.000Z",
                "end": "2025-04-06T23:00:00.000Z"
            },
            {
                "_id": "1737125135608x675007854869741600",
                "name_text": "UA/Messaging Schedule",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1737125191899x740401642376790000"
                ],
                "contributors": [
                    "1737125191899x740401642376790000"
                ],
                "authorisers": [
                    "1737125193842x757847113568354300"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1737125218307x735618772740341800",
                        "source_id": "1737124776586x878893976938872800",
                        "target_id": "1737125135608x675007854869741600",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737124776586x878893976938872800",
                            "name_text": "Coms  Preparation",
                            "start": "2025-03-20T00:00:00.000Z",
                            "end": "2025-03-24T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-03-25T00:00:00.000Z",
                "end": "2025-03-29T00:00:00.000Z"
            },
            {
                "_id": "1737124776586x878893976938872800",
                "name_text": "Coms  Preparation",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1737124842825x474436413166977000"
                ],
                "contributors": [
                    "1737124842825x474436413166977000"
                ],
                "authorisers": [
                    "1737124844485x212994298803650560"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1737124910280x524635094395125800",
                        "source_id": "1737123250919x707845344555171800",
                        "target_id": "1737124776586x878893976938872800",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737123250919x707845344555171800",
                            "name_text": "Communication Strategy",
                            "start": "2025-03-03T00:00:00.000Z",
                            "end": "2025-03-07T00:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1737124917153x184015924553318400",
                        "source_id": "1737124191766x259178409181577200",
                        "target_id": "1737124776586x878893976938872800",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737124191766x259178409181577200",
                            "name_text": "Marketing Assets",
                            "start": "2025-03-12T00:00:00.000Z",
                            "end": "2025-03-19T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-03-20T00:00:00.000Z",
                "end": "2025-03-24T00:00:00.000Z"
            },
            {
                "_id": "1737124542866x572806451471581200",
                "name_text": "Deployment to Staging",
                "approval_time_number": 1,
                "time_period_number": 5,
                "duration": 6,
                "user_contributors_list_custom_user_assignment": [
                    "1737124641129x440827473704779800"
                ],
                "contributors": [
                    "1737124641129x440827473704779800"
                ],
                "authorisers": [
                    "1737124643337x113281915894890500"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1737312915315x163099420078594240",
                        "source_id": "1737123636377x674427925445738500",
                        "target_id": "1737124542866x572806451471581200",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737123636377x674427925445738500",
                            "name_text": "QA Testing",
                            "start": "2025-02-25T00:00:00.000Z",
                            "end": "2025-03-04T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": false,
                "is_fixed": false,
                "priority_option": "Critical (P1)",
                "start": "2025-03-05T00:00:00.000Z",
                "end": "2025-03-11T00:00:00.000Z"
            },
            {
                "_id": "1737124191766x259178409181577200",
                "name_text": "Marketing Assets",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1737124230954x219726762318233600",
                    "1737124233364x742338859509481500"
                ],
                "contributors": [
                    "1737124230954x219726762318233600",
                    "1737124233364x742338859509481500"
                ],
                "authorisers": [
                    "1737124280364x250198159606415360"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1737124292117x681868430548926500",
                        "source_id": "1737123250919x707845344555171800",
                        "target_id": "1737124191766x259178409181577200",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737123250919x707845344555171800",
                            "name_text": "Communication Strategy",
                            "start": "2025-03-03T00:00:00.000Z",
                            "end": "2025-03-07T00:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1737124459855x537850878584946700",
                        "source_id": "1737122675272x156602476261015550",
                        "target_id": "1737124191766x259178409181577200",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737122675272x156602476261015550",
                            "name_text": "Mission Config",
                            "start": "2025-03-03T00:00:00.000Z",
                            "end": "2025-03-07T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-03-12T00:00:00.000Z",
                "end": "2025-03-19T00:00:00.000Z"
            },
            {
                "_id": "1737123933721x615185035844386800",
                "name_text": "In-Game Shop Config",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1737123990469x324868112509829100"
                ],
                "contributors": [
                    "1737123990469x324868112509829100"
                ],
                "authorisers": [
                    "1737123990469x324868112509829100"
                ],
                "position": 1,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1737124024226x658389559421173800",
                        "source_id": "1737122675272x156602476261015550",
                        "target_id": "1737123933721x615185035844386800",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737122675272x156602476261015550",
                            "name_text": "Mission Config",
                            "start": "2025-03-03T00:00:00.000Z",
                            "end": "2025-03-07T00:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1737124033421x435402573314785300",
                        "source_id": "1737122973545x611929186731294700",
                        "target_id": "1737123933721x615185035844386800",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737122973545x611929186731294700",
                            "name_text": "Shop Pricing",
                            "start": "2025-03-03T00:00:00.000Z",
                            "end": "2025-03-07T00:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1737124041054x586957166370619400",
                        "source_id": "1737123250919x707845344555171800",
                        "target_id": "1737123933721x615185035844386800",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737123250919x707845344555171800",
                            "name_text": "Communication Strategy",
                            "start": "2025-03-03T00:00:00.000Z",
                            "end": "2025-03-07T00:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1737128529284x851964756237484000",
                        "source_id": "1737123933721x615185035844386800",
                        "target_id": "1737125317556x967383320181080000",
                        "type": "successor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737125317556x967383320181080000",
                            "name_text": "Deployment to Live",
                            "start": "2025-04-02T23:00:00.000Z",
                            "end": "2025-04-06T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-03-08T00:00:00.000Z",
                "end": "2025-03-12T00:00:00.000Z"
            },
            {
                "_id": "1737123636377x674427925445738500",
                "name_text": "QA Testing",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1737123671587x891499403345920000"
                ],
                "contributors": [
                    "1737123671587x891499403345920000"
                ],
                "authorisers": [
                    "1737123673277x156329451836407800"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1737123714603x829317379345612800",
                        "source_id": "1737121659983x213025941562108960",
                        "target_id": "1737123636377x674427925445738500",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737121659983x213025941562108960",
                            "name_text": "Environment Art",
                            "start": "2025-02-17T00:00:00.000Z",
                            "end": "2025-02-24T00:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1737123721515x761332113085300700",
                        "source_id": "1737122464731x448827088723771400",
                        "target_id": "1737123636377x674427925445738500",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737122464731x448827088723771400",
                            "name_text": "Game Mode Config",
                            "start": "2025-02-26T00:00:00.000Z",
                            "end": "2025-03-02T00:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1737123736664x212963857481596930",
                        "source_id": "1737122273268x278061555317997570",
                        "target_id": "1737123636377x674427925445738500",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737122273268x278061555317997570",
                            "name_text": "Character Cosmetics",
                            "start": "2025-02-17T00:00:00.000Z",
                            "end": "2025-02-24T00:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1737123742763x624861505157857300",
                        "source_id": "1737122048201x115685469230465020",
                        "target_id": "1737123636377x674427925445738500",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737122048201x115685469230465020",
                            "name_text": "Easter Audio Update",
                            "start": "2025-02-17T00:00:00.000Z",
                            "end": "2025-02-24T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": false,
                "is_fixed": false,
                "priority_option": "Critical (P1)",
                "start": "2025-02-25T00:00:00.000Z",
                "end": "2025-03-04T00:00:00.000Z"
            },
            {
                "_id": "1737123250919x707845344555171800",
                "name_text": "Communication Strategy",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1737123315416x183791197012361200"
                ],
                "contributors": [
                    "1737123315416x183791197012361200"
                ],
                "authorisers": [
                    "1737123317616x866925650703548400"
                ],
                "position": 1,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1737123351979x405487043294527500",
                        "source_id": "1737121644207x492734968744596100",
                        "target_id": "1737123250919x707845344555171800",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737121644207x492734968744596100",
                            "name_text": "Concept Design",
                            "start": "2025-02-09T00:00:00.000Z",
                            "end": "2025-02-16T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-03-03T00:00:00.000Z",
                "end": "2025-03-07T00:00:00.000Z"
            },
            {
                "_id": "1737122973545x611929186731294700",
                "name_text": "Shop Pricing",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1737123073750x240808397930758140"
                ],
                "contributors": [
                    "1737123073750x240808397930758140"
                ],
                "authorisers": [
                    "1737123075984x878851337438363600"
                ],
                "position": 2,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1737123081997x844492572493086700",
                        "source_id": "1737121644207x492734968744596100",
                        "target_id": "1737122973545x611929186731294700",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737121644207x492734968744596100",
                            "name_text": "Concept Design",
                            "start": "2025-02-09T00:00:00.000Z",
                            "end": "2025-02-16T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-03-03T00:00:00.000Z",
                "end": "2025-03-07T00:00:00.000Z"
            },
            {
                "_id": "1737122675272x156602476261015550",
                "name_text": "Mission Config",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1737122729914x484274988279398400"
                ],
                "contributors": [
                    "1737122729914x484274988279398400"
                ],
                "authorisers": [
                    "1737122732766x570497437970202600"
                ],
                "position": 3,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1737122963761x247013972770553860",
                        "source_id": "1737122464731x448827088723771400",
                        "target_id": "1737122675272x156602476261015550",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737122464731x448827088723771400",
                            "name_text": "Game Mode Config",
                            "start": "2025-02-26T00:00:00.000Z",
                            "end": "2025-03-02T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-03-03T00:00:00.000Z",
                "end": "2025-03-07T00:00:00.000Z"
            },
            {
                "_id": "1737122464731x448827088723771400",
                "name_text": "Game Mode Config",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1737122538385x760139236460200000"
                ],
                "contributors": [
                    "1737122538385x760139236460200000"
                ],
                "authorisers": [
                    "1737122541555x982478861265010700"
                ],
                "position": 3,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1737122547826x951221968882892800",
                        "source_id": "1737121644207x492734968744596100",
                        "target_id": "1737122464731x448827088723771400",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737121644207x492734968744596100",
                            "name_text": "Concept Design",
                            "start": "2025-02-09T00:00:00.000Z",
                            "end": "2025-02-16T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-02-26T00:00:00.000Z",
                "end": "2025-03-02T00:00:00.000Z"
            },
            {
                "_id": "1737122273268x278061555317997570",
                "name_text": "Character Cosmetics",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1737122338728x852497246742118400"
                ],
                "contributors": [
                    "1737122338728x852497246742118400"
                ],
                "authorisers": [
                    "1737122341276x959986187980505100"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1737122346371x635549009605034000",
                        "source_id": "1737121644207x492734968744596100",
                        "target_id": "1737122273268x278061555317997570",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737121644207x492734968744596100",
                            "name_text": "Concept Design",
                            "start": "2025-02-09T00:00:00.000Z",
                            "end": "2025-02-16T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-02-17T00:00:00.000Z",
                "end": "2025-02-24T00:00:00.000Z"
            },
            {
                "_id": "1737122048201x115685469230465020",
                "name_text": "Easter Audio Update",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1737122129976x551309259384815600"
                ],
                "contributors": [
                    "1737122129976x551309259384815600"
                ],
                "authorisers": [
                    "1737122123519x739701629842096100"
                ],
                "position": 1,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1737122135423x862474182992068600",
                        "source_id": "1737121644207x492734968744596100",
                        "target_id": "1737122048201x115685469230465020",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737121644207x492734968744596100",
                            "name_text": "Concept Design",
                            "start": "2025-02-09T00:00:00.000Z",
                            "end": "2025-02-16T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-02-17T00:00:00.000Z",
                "end": "2025-02-24T00:00:00.000Z"
            },
            {
                "_id": "1737121659983x213025941562108960",
                "name_text": "Environment Art",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1737121919229x322507753079177200"
                ],
                "contributors": [
                    "1737121919229x322507753079177200"
                ],
                "authorisers": [
                    "1737121919229x322507753079177200"
                ],
                "position": 2,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1737121929503x148480278630760450",
                        "source_id": "1737121644207x492734968744596100",
                        "target_id": "1737121659983x213025941562108960",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737121644207x492734968744596100",
                            "name_text": "Concept Design",
                            "start": "2025-02-09T00:00:00.000Z",
                            "end": "2025-02-16T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "start": "2025-02-17T00:00:00.000Z",
                "end": "2025-02-24T00:00:00.000Z"
            },
            {
                "_id": "1737121644207x492734968744596100",
                "name_text": "Concept Design",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1737315042818x608522902601467800"
                ],
                "contributors": [
                    "1737315042818x608522902601467800"
                ],
                "authorisers": [],
                "position": 0,
                "dependencies_list_custom_dependency": [],
                "is_parallel": false,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-02-09T00:00:00.000Z",
                "end": "2025-02-16T00:00:00.000Z"
            }
        ],
        "dependencies": [],
        "work_start_date": "2025-02-09T00:00:00.000Z",
        "work_end_date": "2025-04-14T23:00:00.000Z",
        "authorisers": [
            "1737121808285x201449944468684800",
            "1737121810027x586528269343129600"
        ],
        "deliverable_authorisers": [
            "1737126274085x185557019097825300",
            "1737125875547x508684211797360640",
            "1737125750446x289300667955675140",
            "1737125390523x375378479377809400",
            "1737125193842x757847113568354300",
            "1737124844485x212994298803650560",
            "1737124643337x113281915894890500",
            "1737124280364x250198159606415360",
            "1737123990469x324868112509829100",
            "1737123673277x156329451836407800",
            "1737123317616x866925650703548400",
            "1737123075984x878851337438363600",
            "1737122732766x570497437970202600",
            "1737122541555x982478861265010700",
            "1737122341276x959986187980505100",
            "1737122123519x739701629842096100",
            "1737121919229x322507753079177200"
        ],
        "deliverable_contributors": [
            "1737126272168x170622201734627330",
            "1737125873836x830691410220417000",
            "1737125752487x510732614100058100",
            "1737125390523x375378479377809400",
            "1737125191899x740401642376790000",
            "1737124842825x474436413166977000",
            "1737124641129x440827473704779800",
            "1737124230954x219726762318233600",
            "1737124233364x742338859509481500",
            "1737123990469x324868112509829100",
            "1737123671587x891499403345920000",
            "1737123315416x183791197012361200",
            "1737123073750x240808397930758140",
            "1737122729914x484274988279398400",
            "1737122538385x760139236460200000",
            "1737122338728x852497246742118400",
            "1737122129976x551309259384815600",
            "1737121919229x322507753079177200",
            "1737315042818x608522902601467800"
        ]
    },
    {
        "_id": "1737299532689x151851497938223100",
        "name_text": "Store pricing event",
        "approval_time_number": 2,
        "start_date_date": "2025-01-24T00:00:00.000Z",
        "end_date_date": "2025-04-02T23:00:00.000Z",
        "activity_creator_user": "1736774776172x146105203190919360",
        "deliverables_list_custom_deliverable2": [
            {
                "_id": "1738232379037x670968891686780900",
                "name_text": "joo",
                "approval_time_number": 1,
                "time_period_number": 7,
                "duration": 8,
                "user_contributors_list_custom_user_assignment": [
                    "1738232396678x872584179128729600",
                    "1738232428314x508503968163299300"
                ],
                "contributors": [
                    "1738232396678x872584179128729600",
                    "1738232428314x508503968163299300"
                ],
                "authorisers": [
                    "1738232398714x592254672181919700"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1738751189670x486054054063767550",
                        "source_id": "1737299545205x299056621174354600",
                        "target_id": "1738232379037x670968891686780900",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1737299545205x299056621174354600",
                            "name_text": "Store Pricing/ Sales Events Report",
                            "start": "2025-01-07T00:00:00.000Z",
                            "end": "2025-01-13T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": false,
                "is_fixed": false,
                "priority_option": "Critical (P1)",
                "start": "2025-01-14T00:00:00.000Z",
                "end": "2025-01-22T00:00:00.000Z"
            },
            {
                "_id": "1737299545205x299056621174354600",
                "name_text": "Store Pricing/ Sales Events Report",
                "approval_time_number": 1,
                "time_period_number": 5,
                "duration": 6,
                "user_contributors_list_custom_user_assignment": [
                    "1737299607555x304853164514869250",
                    "1737299610268x628826819438837800",
                    "1738116346166x316735006687887360"
                ],
                "contributors": [
                    "1737299607555x304853164514869250",
                    "1737299610268x628826819438837800",
                    "1738116346166x316735006687887360"
                ],
                "authorisers": [
                    "1737299614241x182216683657363460",
                    "1737299616358x817481449100279800"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [],
                "is_parallel": false,
                "is_fixed": false,
                "priority_option": "Critical (P1)",
                "start": "2025-01-07T00:00:00.000Z",
                "end": "2025-01-13T00:00:00.000Z"
            }
        ],
        "dependencies": [],
        "work_start_date": "2025-01-07T00:00:00.000Z",
        "work_end_date": "2025-01-22T00:00:00.000Z",
        "authorisers": [
            "1737299576488x712196677366448100",
            "1737299675118x553360047358345200"
        ],
        "deliverable_authorisers": [
            "1738232398714x592254672181919700",
            "1737299614241x182216683657363460",
            "1737299616358x817481449100279800"
        ],
        "deliverable_contributors": [
            "1738232396678x872584179128729600",
            "1738232428314x508503968163299300",
            "1737299607555x304853164514869250",
            "1737299610268x628826819438837800",
            "1738116346166x316735006687887360"
        ]
    },
    {
        "_id": "1739452689772x310696741688311800",
        "name_text": "June Jive ",
        "approval_time_number": 2,
        "start_date_date": "2025-06-30T23:00:00.000Z",
        "end_date_date": "2025-06-22T23:00:00.000Z",
        "activity_creator_user": "1727366821155x669597436804614000",
        "deliverables_list_custom_deliverable2": [
            {
                "_id": "1739452706316x404779513985052100",
                "name_text": "Pre-Launch Coms",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1739452706548x310849741786707320"
                ],
                "contributors": [
                    "1739452706548x310849741786707320"
                ],
                "authorisers": [
                    "1739452706517x867953410869551000"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739452710197x226708505739184220",
                        "source_id": "1739452705862x545323182009182850",
                        "target_id": "1739452706316x404779513985052100",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452705862x545323182009182850",
                            "name_text": "Brief Community team",
                            "start": "2025-06-17T23:00:00.000Z",
                            "end": "2025-06-20T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-06-21T23:00:00.000Z",
                "end": "2025-06-28T23:00:00.000Z"
            },
            {
                "_id": "1739452705862x545323182009182850",
                "name_text": "Brief Community team",
                "approval_time_number": 1,
                "time_period_number": 2,
                "duration": 3,
                "user_contributors_list_custom_user_assignment": [
                    "1739452706078x223575433892686530"
                ],
                "contributors": [
                    "1739452706078x223575433892686530"
                ],
                "authorisers": [
                    "1739452706030x332072658795020900"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739452710440x900348855000251400",
                        "source_id": "1739452704319x799514923880332300",
                        "target_id": "1739452705862x545323182009182850",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452704319x799514923880332300",
                            "name_text": "UA/Messaging Schedule",
                            "start": "2025-06-12T23:00:00.000Z",
                            "end": "2025-06-16T23:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739452710532x315655669034451650",
                        "source_id": "1739452703928x110399360266030030",
                        "target_id": "1739452705862x545323182009182850",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452703928x110399360266030030",
                            "name_text": "Coms  Preparation",
                            "start": "2025-06-07T23:00:00.000Z",
                            "end": "2025-06-11T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-06-17T23:00:00.000Z",
                "end": "2025-06-20T23:00:00.000Z"
            },
            {
                "_id": "1739452705268x557544605432373800",
                "name_text": "Ops Team Briefing",
                "approval_time_number": 1,
                "time_period_number": 2,
                "duration": 3,
                "user_contributors_list_custom_user_assignment": [
                    "1739452705571x193032747941429540"
                ],
                "contributors": [
                    "1739452705571x193032747941429540"
                ],
                "authorisers": [
                    "1739452705765x701041945203060500"
                ],
                "position": 2,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739452710820x614970305924445300",
                        "source_id": "1739452703499x961952384953437700",
                        "target_id": "1739452705268x557544605432373800",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452703499x961952384953437700",
                            "name_text": "Deployment to Staging",
                            "start": "2025-05-18T23:00:00.000Z",
                            "end": "2025-05-24T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-05-26T23:00:00.000Z",
                "end": "2025-05-29T23:00:00.000Z"
            },
            {
                "_id": "1739452704779x664678248914962700",
                "name_text": "Deployment to Live",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1739452704984x505246503379641400"
                ],
                "contributors": [
                    "1739452704984x505246503379641400"
                ],
                "authorisers": [
                    "1739452704939x521470886833721400"
                ],
                "position": 1,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739452711031x351020741395787300",
                        "source_id": "1739452703499x961952384953437700",
                        "target_id": "1739452704779x664678248914962700",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452703499x961952384953437700",
                            "name_text": "Deployment to Staging",
                            "start": "2025-05-18T23:00:00.000Z",
                            "end": "2025-05-24T23:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739452711138x370382638065255740",
                        "source_id": "1739452705268x557544605432373800",
                        "target_id": "1739452704779x664678248914962700",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452705268x557544605432373800",
                            "name_text": "Ops Team Briefing",
                            "start": "2025-05-26T23:00:00.000Z",
                            "end": "2025-05-29T23:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739452711270x476726838506796860",
                        "source_id": "1739452705862x545323182009182850",
                        "target_id": "1739452704779x664678248914962700",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452705862x545323182009182850",
                            "name_text": "Brief Community team",
                            "start": "2025-06-17T23:00:00.000Z",
                            "end": "2025-06-20T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "start": "2025-06-24T23:00:00.000Z",
                "end": "2025-06-28T23:00:00.000Z"
            },
            {
                "_id": "1739452704319x799514923880332300",
                "name_text": "UA/Messaging Schedule",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1739452704558x506235431720081900"
                ],
                "contributors": [
                    "1739452704558x506235431720081900"
                ],
                "authorisers": [
                    "1739452704552x605105101275618200"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739452711452x569747825410145340",
                        "source_id": "1739452703928x110399360266030030",
                        "target_id": "1739452704319x799514923880332300",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452703928x110399360266030030",
                            "name_text": "Coms  Preparation",
                            "start": "2025-06-07T23:00:00.000Z",
                            "end": "2025-06-11T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-06-12T23:00:00.000Z",
                "end": "2025-06-16T23:00:00.000Z"
            },
            {
                "_id": "1739452703928x110399360266030030",
                "name_text": "Coms  Preparation",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1739452704210x965652429488069200"
                ],
                "contributors": [
                    "1739452704210x965652429488069200"
                ],
                "authorisers": [
                    "1739452704270x691895730341748100"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739452711656x883720099858612600",
                        "source_id": "1739452701379x522613154644168770",
                        "target_id": "1739452703928x110399360266030030",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452701379x522613154644168770",
                            "name_text": "Communication Strategy",
                            "start": "2025-05-25T23:00:00.000Z",
                            "end": "2025-05-29T23:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739452711753x487591378775535940",
                        "source_id": "1739452703022x129240149764285890",
                        "target_id": "1739452703928x110399360266030030",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452703022x129240149764285890",
                            "name_text": "Marketing Assets",
                            "start": "2025-05-30T23:00:00.000Z",
                            "end": "2025-06-06T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-06-07T23:00:00.000Z",
                "end": "2025-06-11T23:00:00.000Z"
            },
            {
                "_id": "1739452703499x961952384953437700",
                "name_text": "Deployment to Staging",
                "approval_time_number": 1,
                "time_period_number": 5,
                "duration": 6,
                "user_contributors_list_custom_user_assignment": [
                    "1739452703736x305575227801310400"
                ],
                "contributors": [
                    "1739452703736x305575227801310400"
                ],
                "authorisers": [
                    "1739452703722x124432683740435040"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739452711951x513323466521655760",
                        "source_id": "1739452701843x866383211045391700",
                        "target_id": "1739452703499x961952384953437700",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452701843x866383211045391700",
                            "name_text": "QA Testing",
                            "start": "2025-05-10T23:00:00.000Z",
                            "end": "2025-05-17T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": false,
                "is_fixed": false,
                "priority_option": "Critical (P1)",
                "start": "2025-05-18T23:00:00.000Z",
                "end": "2025-05-24T23:00:00.000Z"
            },
            {
                "_id": "1739452703022x129240149764285890",
                "name_text": "Marketing Assets",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1739452703581x968891925703897500"
                ],
                "contributors": [
                    "1739452703581x968891925703897500"
                ],
                "authorisers": [
                    "1739452703216x768781689407017900"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739452712263x721430845310207900",
                        "source_id": "1739452701379x522613154644168770",
                        "target_id": "1739452703022x129240149764285890",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452701379x522613154644168770",
                            "name_text": "Communication Strategy",
                            "start": "2025-05-25T23:00:00.000Z",
                            "end": "2025-05-29T23:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739452712361x467176763388064400",
                        "source_id": "1739452700305x960909407354934700",
                        "target_id": "1739452703022x129240149764285890",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452700305x960909407354934700",
                            "name_text": "Mission Config",
                            "start": "2025-05-05T23:00:00.000Z",
                            "end": "2025-05-09T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-05-30T23:00:00.000Z",
                "end": "2025-06-06T23:00:00.000Z"
            },
            {
                "_id": "1739452702390x970332422807148500",
                "name_text": "In-Game Shop Config",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1739452702612x866793801164552000"
                ],
                "contributors": [
                    "1739452702612x866793801164552000"
                ],
                "authorisers": [
                    "1739452702750x114527803288442030"
                ],
                "position": 1,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739452712593x528858904809675000",
                        "source_id": "1739452700305x960909407354934700",
                        "target_id": "1739452702390x970332422807148500",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452700305x960909407354934700",
                            "name_text": "Mission Config",
                            "start": "2025-05-05T23:00:00.000Z",
                            "end": "2025-05-09T23:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739452712686x876782405764782300",
                        "source_id": "1739452700903x568918936311148200",
                        "target_id": "1739452702390x970332422807148500",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452700903x568918936311148200",
                            "name_text": "Shop Pricing",
                            "start": "2025-05-25T23:00:00.000Z",
                            "end": "2025-05-29T23:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739452712783x387244996303185660",
                        "source_id": "1739452701379x522613154644168770",
                        "target_id": "1739452702390x970332422807148500",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452701379x522613154644168770",
                            "name_text": "Communication Strategy",
                            "start": "2025-05-25T23:00:00.000Z",
                            "end": "2025-05-29T23:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739452712888x980809195835153300",
                        "source_id": "1739452702390x970332422807148500",
                        "target_id": "1739452704779x664678248914962700",
                        "type": "successor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452704779x664678248914962700",
                            "name_text": "Deployment to Live",
                            "start": "2025-06-24T23:00:00.000Z",
                            "end": "2025-06-28T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-06-02T23:00:00.000Z",
                "end": "2025-06-06T23:00:00.000Z"
            },
            {
                "_id": "1739452701843x866383211045391700",
                "name_text": "QA Testing",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1739452702060x864532098774446200"
                ],
                "contributors": [
                    "1739452702060x864532098774446200"
                ],
                "authorisers": [
                    "1739452702084x880754052779751000"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739452713151x300455967089874300",
                        "source_id": "1739452698215x811001302154144900",
                        "target_id": "1739452701843x866383211045391700",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452698215x811001302154144900",
                            "name_text": "Environment Art",
                            "start": "2025-04-27T23:00:00.000Z",
                            "end": "2025-05-04T23:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739452713249x209498351092867620",
                        "source_id": "1739452699816x977039397812468500",
                        "target_id": "1739452701843x866383211045391700",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452699816x977039397812468500",
                            "name_text": "Game Mode Config",
                            "start": "2025-04-30T23:00:00.000Z",
                            "end": "2025-05-04T23:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739452713339x975891071042684400",
                        "source_id": "1739452699290x426297328122414100",
                        "target_id": "1739452701843x866383211045391700",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452699290x426297328122414100",
                            "name_text": "Character Cosmetics",
                            "start": "2025-04-27T23:00:00.000Z",
                            "end": "2025-05-04T23:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739452713481x672401669485180900",
                        "source_id": "1739452698833x535976466279298500",
                        "target_id": "1739452701843x866383211045391700",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452698833x535976466279298500",
                            "name_text": "Easter Audio Update",
                            "start": "2025-04-27T23:00:00.000Z",
                            "end": "2025-05-04T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": false,
                "is_fixed": false,
                "priority_option": "Critical (P1)",
                "start": "2025-05-10T23:00:00.000Z",
                "end": "2025-05-17T23:00:00.000Z"
            },
            {
                "_id": "1739452701379x522613154644168770",
                "name_text": "Communication Strategy",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1739452701669x188601799198819600"
                ],
                "contributors": [
                    "1739452701669x188601799198819600"
                ],
                "authorisers": [
                    "1739452701582x809000101046998000"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739453768080x963908624417816600",
                        "source_id": "1739452697790x935073169790248200",
                        "target_id": "1739452701379x522613154644168770",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452697790x935073169790248200",
                            "name_text": "Concept Design",
                            "start": "2025-04-19T23:00:00.000Z",
                            "end": "2025-04-26T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-05-25T23:00:00.000Z",
                "end": "2025-05-29T23:00:00.000Z"
            },
            {
                "_id": "1739452700903x568918936311148200",
                "name_text": "Shop Pricing",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1739452701099x574089662204113400"
                ],
                "contributors": [
                    "1739452701099x574089662204113400"
                ],
                "authorisers": [
                    "1739452701257x444983419960232500"
                ],
                "position": 1,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739452713915x916425521899323800",
                        "source_id": "1739452697790x935073169790248200",
                        "target_id": "1739452700903x568918936311148200",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452697790x935073169790248200",
                            "name_text": "Concept Design",
                            "start": "2025-04-19T23:00:00.000Z",
                            "end": "2025-04-26T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-05-25T23:00:00.000Z",
                "end": "2025-05-29T23:00:00.000Z"
            },
            {
                "_id": "1739452700305x960909407354934700",
                "name_text": "Mission Config",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1739452700746x405112724207285200"
                ],
                "contributors": [
                    "1739452700746x405112724207285200"
                ],
                "authorisers": [
                    "1739452700595x603706183615633300"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739452714366x763723083762119600",
                        "source_id": "1739452699816x977039397812468500",
                        "target_id": "1739452700305x960909407354934700",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452699816x977039397812468500",
                            "name_text": "Game Mode Config",
                            "start": "2025-04-30T23:00:00.000Z",
                            "end": "2025-05-04T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-05-05T23:00:00.000Z",
                "end": "2025-05-09T23:00:00.000Z"
            },
            {
                "_id": "1739452699816x977039397812468500",
                "name_text": "Game Mode Config",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1739452700065x391873724875250400"
                ],
                "contributors": [
                    "1739452700065x391873724875250400"
                ],
                "authorisers": [
                    "1739452700045x447824357285305340"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1742478754961x645368398421164000",
                        "source_id": "1739452697790x935073169790248200",
                        "target_id": "1739452699816x977039397812468500",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452697790x935073169790248200",
                            "name_text": "Concept Design",
                            "start": "2025-04-19T23:00:00.000Z",
                            "end": "2025-04-26T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-04-30T23:00:00.000Z",
                "end": "2025-05-04T23:00:00.000Z"
            },
            {
                "_id": "1739452699290x426297328122414100",
                "name_text": "Character Cosmetics",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1739452699596x607165080657184600"
                ],
                "contributors": [
                    "1739452699596x607165080657184600"
                ],
                "authorisers": [
                    "1739452699513x575354656247972800"
                ],
                "position": 1,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739453999534x825178131782434800",
                        "source_id": "1739452697790x935073169790248200",
                        "target_id": "1739452699290x426297328122414100",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452697790x935073169790248200",
                            "name_text": "Concept Design",
                            "start": "2025-04-19T23:00:00.000Z",
                            "end": "2025-04-26T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-04-27T23:00:00.000Z",
                "end": "2025-05-04T23:00:00.000Z"
            },
            {
                "_id": "1739452698833x535976466279298500",
                "name_text": "Easter Audio Update",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1739452699250x743991675634421400"
                ],
                "contributors": [
                    "1739452699250x743991675634421400"
                ],
                "authorisers": [
                    "1739452699084x498190077120600260"
                ],
                "position": 2,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739453892997x733168428668682200",
                        "source_id": "1739452697790x935073169790248200",
                        "target_id": "1739452698833x535976466279298500",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452697790x935073169790248200",
                            "name_text": "Concept Design",
                            "start": "2025-04-19T23:00:00.000Z",
                            "end": "2025-04-26T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-04-27T23:00:00.000Z",
                "end": "2025-05-04T23:00:00.000Z"
            },
            {
                "_id": "1739452698215x811001302154144900",
                "name_text": "Environment Art",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1739452698452x795781138431519200"
                ],
                "contributors": [
                    "1739452698452x795781138431519200"
                ],
                "authorisers": [
                    "1739452698468x905486783971858300"
                ],
                "position": 3,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1742483999921x667033074726600700",
                        "source_id": "1739452698215x811001302154144900",
                        "target_id": "1739452703022x129240149764285890",
                        "type": "successor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452703022x129240149764285890",
                            "name_text": "Marketing Assets",
                            "start": "2025-05-30T23:00:00.000Z",
                            "end": "2025-06-06T23:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1743002934062x560348039398031360",
                        "source_id": "1739452697790x935073169790248200",
                        "target_id": "1739452698215x811001302154144900",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739452697790x935073169790248200",
                            "name_text": "Concept Design",
                            "start": "2025-04-19T23:00:00.000Z",
                            "end": "2025-04-26T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "start": "2025-04-27T23:00:00.000Z",
                "end": "2025-05-04T23:00:00.000Z"
            },
            {
                "_id": "1739452697790x935073169790248200",
                "name_text": "Concept Design",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [],
                "contributors": [],
                "authorisers": [],
                "position": 0,
                "dependencies_list_custom_dependency": [],
                "is_parallel": false,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-04-19T23:00:00.000Z",
                "end": "2025-04-26T23:00:00.000Z"
            }
        ],
        "dependencies": [],
        "work_start_date": "2025-04-19T23:00:00.000Z",
        "work_end_date": "2025-06-28T23:00:00.000Z",
        "authorisers": [
            "1739453424684x431510214792183800"
        ],
        "deliverable_authorisers": [
            "1739452706517x867953410869551000",
            "1739452706030x332072658795020900",
            "1739452705765x701041945203060500",
            "1739452704939x521470886833721400",
            "1739452704552x605105101275618200",
            "1739452704270x691895730341748100",
            "1739452703722x124432683740435040",
            "1739452703216x768781689407017900",
            "1739452702750x114527803288442030",
            "1739452702084x880754052779751000",
            "1739452701582x809000101046998000",
            "1739452701257x444983419960232500",
            "1739452700595x603706183615633300",
            "1739452700045x447824357285305340",
            "1739452699513x575354656247972800",
            "1739452699084x498190077120600260",
            "1739452698468x905486783971858300"
        ],
        "deliverable_contributors": [
            "1739452706548x310849741786707320",
            "1739452706078x223575433892686530",
            "1739452705571x193032747941429540",
            "1739452704984x505246503379641400",
            "1739452704558x506235431720081900",
            "1739452704210x965652429488069200",
            "1739452703736x305575227801310400",
            "1739452703581x968891925703897500",
            "1739452702612x866793801164552000",
            "1739452702060x864532098774446200",
            "1739452701669x188601799198819600",
            "1739452701099x574089662204113400",
            "1739452700746x405112724207285200",
            "1739452700065x391873724875250400",
            "1739452699596x607165080657184600",
            "1739452699250x743991675634421400",
            "1739452698452x795781138431519200"
        ]
    },
    {
        "_id": "1739455403867x385819467736154100",
        "name_text": "july jungle jam",
        "approval_time_number": 2,
        "start_date_date": "2025-06-30T22:00:00.000Z",
        "end_date_date": "2025-07-30T22:00:00.000Z",
        "activity_creator_user": "1736779036824x713220502225218000",
        "deliverables_list_custom_deliverable2": [
            {
                "_id": "1739455425364x294807610679900700",
                "name_text": "Pre-Launch Coms",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1739455425671x743113643881550200"
                ],
                "contributors": [
                    "1739455425671x743113643881550200"
                ],
                "authorisers": [
                    "1739455425599x707392095239597600"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739455433363x880191095429950600",
                        "source_id": "1739455424369x511281302538063170",
                        "target_id": "1739455425364x294807610679900700",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739455424369x511281302538063170",
                            "name_text": "Brief Community team",
                            "start": "2025-06-12T22:00:00.000Z",
                            "end": "2025-06-15T22:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-06-21T22:00:00.000Z",
                "end": "2025-06-28T22:00:00.000Z"
            },
            {
                "_id": "1739455424369x511281302538063170",
                "name_text": "Brief Community team",
                "approval_time_number": 1,
                "time_period_number": 2,
                "duration": 3,
                "user_contributors_list_custom_user_assignment": [
                    "1739455424812x127748696534626830"
                ],
                "contributors": [
                    "1739455424812x127748696534626830"
                ],
                "authorisers": [
                    "1739455424798x245440432924934460"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739455434105x142571377788666350",
                        "source_id": "1739455422492x256020842645172220",
                        "target_id": "1739455424369x511281302538063170",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739455422492x256020842645172220",
                            "name_text": "UA/Messaging Schedule",
                            "start": "2025-06-07T22:00:00.000Z",
                            "end": "2025-06-11T22:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739455434273x923515741126008700",
                        "source_id": "1739455421929x753275795463304100",
                        "target_id": "1739455424369x511281302538063170",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739455421929x753275795463304100",
                            "name_text": "Coms  Preparation",
                            "start": "2025-06-02T22:00:00.000Z",
                            "end": "2025-06-06T22:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-06-12T22:00:00.000Z",
                "end": "2025-06-15T22:00:00.000Z"
            },
            {
                "_id": "1739455423553x672420648489535900",
                "name_text": "Ops Team Briefing",
                "approval_time_number": 1,
                "time_period_number": 2,
                "duration": 3,
                "user_contributors_list_custom_user_assignment": [
                    "1739455423821x375850757275642000"
                ],
                "contributors": [
                    "1739455423821x375850757275642000"
                ],
                "authorisers": [
                    "1739455423890x968739315010351100"
                ],
                "position": 1,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739455434537x664250952439108200",
                        "source_id": "1739455421264x952798754404836600",
                        "target_id": "1739455423553x672420648489535900",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739455421264x952798754404836600",
                            "name_text": "Deployment to Staging",
                            "start": "2025-05-18T22:00:00.000Z",
                            "end": "2025-05-24T22:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-06-12T22:00:00.000Z",
                "end": "2025-06-15T22:00:00.000Z"
            },
            {
                "_id": "1739455423039x838591148768511900",
                "name_text": "Deployment to Live",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1739455423308x342908985072252200"
                ],
                "contributors": [
                    "1739455423308x342908985072252200"
                ],
                "authorisers": [
                    "1739455423343x885756329987926800"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739455434801x466265089060028860",
                        "source_id": "1739455421264x952798754404836600",
                        "target_id": "1739455423039x838591148768511900",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739455421264x952798754404836600",
                            "name_text": "Deployment to Staging",
                            "start": "2025-05-18T22:00:00.000Z",
                            "end": "2025-05-24T22:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739455434890x505808772514447800",
                        "source_id": "1739455423553x672420648489535900",
                        "target_id": "1739455423039x838591148768511900",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739455423553x672420648489535900",
                            "name_text": "Ops Team Briefing",
                            "start": "2025-06-12T22:00:00.000Z",
                            "end": "2025-06-15T22:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739455435058x854730093842093400",
                        "source_id": "1739455424369x511281302538063170",
                        "target_id": "1739455423039x838591148768511900",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739455424369x511281302538063170",
                            "name_text": "Brief Community team",
                            "start": "2025-06-12T22:00:00.000Z",
                            "end": "2025-06-15T22:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": false,
                "is_fixed": false,
                "start": "2025-06-16T22:00:00.000Z",
                "end": "2025-06-20T22:00:00.000Z"
            },
            {
                "_id": "1739455422492x256020842645172220",
                "name_text": "UA/Messaging Schedule",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1739455422719x397087622850472960"
                ],
                "contributors": [
                    "1739455422719x397087622850472960"
                ],
                "authorisers": [
                    "1739455422680x158244988464291800"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739455435293x368341347005073800",
                        "source_id": "1739455421929x753275795463304100",
                        "target_id": "1739455422492x256020842645172220",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739455421929x753275795463304100",
                            "name_text": "Coms  Preparation",
                            "start": "2025-06-02T22:00:00.000Z",
                            "end": "2025-06-06T22:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-06-07T22:00:00.000Z",
                "end": "2025-06-11T22:00:00.000Z"
            },
            {
                "_id": "1739455421929x753275795463304100",
                "name_text": "Coms  Preparation",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1739455422381x206539495083920770"
                ],
                "contributors": [
                    "1739455422381x206539495083920770"
                ],
                "authorisers": [
                    "1739455422382x866431442665264100"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739455435531x929346457729602400",
                        "source_id": "1739455418744x992797226604546800",
                        "target_id": "1739455421929x753275795463304100",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739455418744x992797226604546800",
                            "name_text": "Communication Strategy",
                            "start": "2025-05-16T22:00:00.000Z",
                            "end": "2025-05-20T22:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739455435667x542926291495950500",
                        "source_id": "1739455420744x716206770626503800",
                        "target_id": "1739455421929x753275795463304100",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739455420744x716206770626503800",
                            "name_text": "Marketing Assets",
                            "start": "2025-05-25T22:00:00.000Z",
                            "end": "2025-06-01T22:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-06-02T22:00:00.000Z",
                "end": "2025-06-06T22:00:00.000Z"
            },
            {
                "_id": "1739455421264x952798754404836600",
                "name_text": "Deployment to Staging",
                "approval_time_number": 1,
                "time_period_number": 5,
                "duration": 6,
                "user_contributors_list_custom_user_assignment": [
                    "1739455421503x716279315121923100"
                ],
                "contributors": [
                    "1739455421503x716279315121923100"
                ],
                "authorisers": [
                    "1739455421582x961770852660613500"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739455435962x789375806859617300",
                        "source_id": "1739455419295x314893500666210560",
                        "target_id": "1739455421264x952798754404836600",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739455419295x314893500666210560",
                            "name_text": "QA Testing",
                            "start": "2025-05-10T22:00:00.000Z",
                            "end": "2025-05-17T22:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": false,
                "is_fixed": false,
                "priority_option": "Critical (P1)",
                "start": "2025-05-18T22:00:00.000Z",
                "end": "2025-05-24T22:00:00.000Z"
            },
            {
                "_id": "1739455420744x716206770626503800",
                "name_text": "Marketing Assets",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1739455421023x446109328442006600"
                ],
                "contributors": [
                    "1739455421023x446109328442006600"
                ],
                "authorisers": [
                    "1739455420948x836013017677089900"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739455436222x115746108128511170",
                        "source_id": "1739455418744x992797226604546800",
                        "target_id": "1739455420744x716206770626503800",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739455418744x992797226604546800",
                            "name_text": "Communication Strategy",
                            "start": "2025-05-16T22:00:00.000Z",
                            "end": "2025-05-20T22:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739455436356x709944207565190000",
                        "source_id": "1739455417701x647067944179253500",
                        "target_id": "1739455420744x716206770626503800",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739455417701x647067944179253500",
                            "name_text": "Mission Config",
                            "start": "2025-05-16T22:00:00.000Z",
                            "end": "2025-05-20T22:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-05-25T22:00:00.000Z",
                "end": "2025-06-01T22:00:00.000Z"
            },
            {
                "_id": "1739455420083x575765288019824800",
                "name_text": "In-Game Shop Config",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1739455420496x735132078287842300"
                ],
                "contributors": [
                    "1739455420496x735132078287842300"
                ],
                "authorisers": [
                    "1739455420474x507540114530730750"
                ],
                "position": 1,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739455436670x135405188818549680",
                        "source_id": "1739455417701x647067944179253500",
                        "target_id": "1739455420083x575765288019824800",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739455417701x647067944179253500",
                            "name_text": "Mission Config",
                            "start": "2025-05-16T22:00:00.000Z",
                            "end": "2025-05-20T22:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739455436861x403470972845738430",
                        "source_id": "1739455418274x322244024319593440",
                        "target_id": "1739455420083x575765288019824800",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739455418274x322244024319593440",
                            "name_text": "Shop Pricing",
                            "start": "2025-05-16T22:00:00.000Z",
                            "end": "2025-05-20T22:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739455437224x596654371989682800",
                        "source_id": "1739455418744x992797226604546800",
                        "target_id": "1739455420083x575765288019824800",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739455418744x992797226604546800",
                            "name_text": "Communication Strategy",
                            "start": "2025-05-16T22:00:00.000Z",
                            "end": "2025-05-20T22:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739455437430x492557258322892100",
                        "source_id": "1739455420083x575765288019824800",
                        "target_id": "1739455423039x838591148768511900",
                        "type": "successor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739455423039x838591148768511900",
                            "name_text": "Deployment to Live",
                            "start": "2025-06-16T22:00:00.000Z",
                            "end": "2025-06-20T22:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-05-21T22:00:00.000Z",
                "end": "2025-05-25T22:00:00.000Z"
            },
            {
                "_id": "1739455419295x314893500666210560",
                "name_text": "QA Testing",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1739455419606x819120359772199400"
                ],
                "contributors": [
                    "1739455419606x819120359772199400"
                ],
                "authorisers": [
                    "1739455419549x713105564276026900"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739455438113x237030318497307040",
                        "source_id": "1739455414586x231909860386044100",
                        "target_id": "1739455419295x314893500666210560",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739455414586x231909860386044100",
                            "name_text": "Environment Art",
                            "start": "2025-05-02T22:00:00.000Z",
                            "end": "2025-05-09T22:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739455438232x663601914364216200",
                        "source_id": "1739455416844x595902496879643800",
                        "target_id": "1739455419295x314893500666210560",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739455416844x595902496879643800",
                            "name_text": "Game Mode Config",
                            "start": "2025-05-11T22:00:00.000Z",
                            "end": "2025-05-15T22:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739455438342x372571603610657000",
                        "source_id": "1739455416066x594191190052718100",
                        "target_id": "1739455419295x314893500666210560",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739455416066x594191190052718100",
                            "name_text": "Character Cosmetics",
                            "start": "2025-05-02T22:00:00.000Z",
                            "end": "2025-05-09T22:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739455438435x173570639231855900",
                        "source_id": "1739455415566x648002092854374800",
                        "target_id": "1739455419295x314893500666210560",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739455415566x648002092854374800",
                            "name_text": "Easter Audio Update",
                            "start": "2025-05-02T22:00:00.000Z",
                            "end": "2025-05-09T22:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": false,
                "is_fixed": false,
                "priority_option": "Critical (P1)",
                "start": "2025-05-10T22:00:00.000Z",
                "end": "2025-05-17T22:00:00.000Z"
            },
            {
                "_id": "1739455418744x992797226604546800",
                "name_text": "Communication Strategy",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1739455419001x564643864990970200"
                ],
                "contributors": [
                    "1739455419001x564643864990970200"
                ],
                "authorisers": [
                    "1739455419007x905645908178313500"
                ],
                "position": 1,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739455438766x661166124694262800",
                        "source_id": "1739455414050x750383567121913500",
                        "target_id": "1739455418744x992797226604546800",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739455414050x750383567121913500",
                            "name_text": "Concept Design",
                            "start": "2025-04-24T22:00:00.000Z",
                            "end": "2025-05-01T22:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-05-16T22:00:00.000Z",
                "end": "2025-05-20T22:00:00.000Z"
            },
            {
                "_id": "1739455418274x322244024319593440",
                "name_text": "Shop Pricing",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1739455418508x944021092217958400"
                ],
                "contributors": [
                    "1739455418508x944021092217958400"
                ],
                "authorisers": [
                    "1739455418519x521268713575893760"
                ],
                "position": 2,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739455439181x290895366535060860",
                        "source_id": "1739455414050x750383567121913500",
                        "target_id": "1739455418274x322244024319593440",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739455414050x750383567121913500",
                            "name_text": "Concept Design",
                            "start": "2025-04-24T22:00:00.000Z",
                            "end": "2025-05-01T22:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-05-16T22:00:00.000Z",
                "end": "2025-05-20T22:00:00.000Z"
            },
            {
                "_id": "1739455417701x647067944179253500",
                "name_text": "Mission Config",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1739455417942x445085130113008800"
                ],
                "contributors": [
                    "1739455417942x445085130113008800"
                ],
                "authorisers": [
                    "1739455417948x606379137731761800"
                ],
                "position": 3,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739455439384x140745830864528620",
                        "source_id": "1739455416844x595902496879643800",
                        "target_id": "1739455417701x647067944179253500",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739455416844x595902496879643800",
                            "name_text": "Game Mode Config",
                            "start": "2025-05-11T22:00:00.000Z",
                            "end": "2025-05-15T22:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-05-16T22:00:00.000Z",
                "end": "2025-05-20T22:00:00.000Z"
            },
            {
                "_id": "1739455416844x595902496879643800",
                "name_text": "Game Mode Config",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1739455417489x311147026714226900"
                ],
                "contributors": [
                    "1739455417489x311147026714226900"
                ],
                "authorisers": [
                    "1739455417288x216006380809883780"
                ],
                "position": 3,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739455439820x607220877993590500",
                        "source_id": "1739455414050x750383567121913500",
                        "target_id": "1739455416844x595902496879643800",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739455414050x750383567121913500",
                            "name_text": "Concept Design",
                            "start": "2025-04-24T22:00:00.000Z",
                            "end": "2025-05-01T22:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-05-11T22:00:00.000Z",
                "end": "2025-05-15T22:00:00.000Z"
            },
            {
                "_id": "1739455416066x594191190052718100",
                "name_text": "Character Cosmetics",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1739455416345x285964052396062050"
                ],
                "contributors": [
                    "1739455416345x285964052396062050"
                ],
                "authorisers": [
                    "1739455416362x287108844347422560"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739455440366x179199240234184640",
                        "source_id": "1739455414050x750383567121913500",
                        "target_id": "1739455416066x594191190052718100",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739455414050x750383567121913500",
                            "name_text": "Concept Design",
                            "start": "2025-04-24T22:00:00.000Z",
                            "end": "2025-05-01T22:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-05-02T22:00:00.000Z",
                "end": "2025-05-09T22:00:00.000Z"
            },
            {
                "_id": "1739455415566x648002092854374800",
                "name_text": "Easter Audio Update",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1739455415817x479675763861769200"
                ],
                "contributors": [
                    "1739455415817x479675763861769200"
                ],
                "authorisers": [
                    "1739455415891x509421123362415740"
                ],
                "position": 1,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739455440665x317944914220837250",
                        "source_id": "1739455414050x750383567121913500",
                        "target_id": "1739455415566x648002092854374800",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739455414050x750383567121913500",
                            "name_text": "Concept Design",
                            "start": "2025-04-24T22:00:00.000Z",
                            "end": "2025-05-01T22:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-05-02T22:00:00.000Z",
                "end": "2025-05-09T22:00:00.000Z"
            },
            {
                "_id": "1739455414586x231909860386044100",
                "name_text": "Environment Art",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1739455414860x699152425849410600"
                ],
                "contributors": [
                    "1739455414860x699152425849410600"
                ],
                "authorisers": [
                    "1739455414852x524806370689431040"
                ],
                "position": 2,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739455440862x973654805564562200",
                        "source_id": "1739455414050x750383567121913500",
                        "target_id": "1739455414586x231909860386044100",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739455414050x750383567121913500",
                            "name_text": "Concept Design",
                            "start": "2025-04-24T22:00:00.000Z",
                            "end": "2025-05-01T22:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "start": "2025-05-02T22:00:00.000Z",
                "end": "2025-05-09T22:00:00.000Z"
            },
            {
                "_id": "1739455414050x750383567121913500",
                "name_text": "Concept Design",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [],
                "contributors": [],
                "authorisers": [],
                "position": 0,
                "dependencies_list_custom_dependency": [],
                "is_parallel": false,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-04-24T22:00:00.000Z",
                "end": "2025-05-01T22:00:00.000Z"
            }
        ],
        "dependencies": [],
        "work_start_date": "2025-04-24T22:00:00.000Z",
        "work_end_date": "2025-06-28T22:00:00.000Z",
        "authorisers": [
            "1739455413822x168639365637318180",
            "1739455414128x851688503746050900"
        ],
        "deliverable_authorisers": [
            "1739455425599x707392095239597600",
            "1739455424798x245440432924934460",
            "1739455423890x968739315010351100",
            "1739455423343x885756329987926800",
            "1739455422680x158244988464291800",
            "1739455422382x866431442665264100",
            "1739455421582x961770852660613500",
            "1739455420948x836013017677089900",
            "1739455420474x507540114530730750",
            "1739455419549x713105564276026900",
            "1739455419007x905645908178313500",
            "1739455418519x521268713575893760",
            "1739455417948x606379137731761800",
            "1739455417288x216006380809883780",
            "1739455416362x287108844347422560",
            "1739455415891x509421123362415740",
            "1739455414852x524806370689431040"
        ],
        "deliverable_contributors": [
            "1739455425671x743113643881550200",
            "1739455424812x127748696534626830",
            "1739455423821x375850757275642000",
            "1739455423308x342908985072252200",
            "1739455422719x397087622850472960",
            "1739455422381x206539495083920770",
            "1739455421503x716279315121923100",
            "1739455421023x446109328442006600",
            "1739455420496x735132078287842300",
            "1739455419606x819120359772199400",
            "1739455419001x564643864990970200",
            "1739455418508x944021092217958400",
            "1739455417942x445085130113008800",
            "1739455417489x311147026714226900",
            "1739455416345x285964052396062050",
            "1739455415817x479675763861769200",
            "1739455414860x699152425849410600"
        ]
    },
    {
        "_id": "1739981930984x434334345026863100",
        "name_text": "Current Activity",
        "approval_time_number": 0,
        "start_date_date": "2025-07-01T23:00:00.000Z",
        "end_date_date": null,
        "activity_creator_user": "1736774776172x146105203190919360",
        "deliverables_list_custom_deliverable2": [
            {
                "_id": "1739981967565x445698340259645950",
                "name_text": "Pre-Launch Coms",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1739981967854x388818745902505400"
                ],
                "contributors": [
                    "1739981967854x388818745902505400"
                ],
                "authorisers": [
                    "1739981967747x407208325065166600"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739981968060x446274945353578200",
                        "source_id": "1739981967316x740735235989814300",
                        "target_id": "1739981967565x445698340259645950",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739981967316x740735235989814300",
                            "name_text": "Brief Community team",
                            "start": "2025-06-15T23:00:00.000Z",
                            "end": "2025-06-18T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-06-24T23:00:00.000Z",
                "end": "2025-07-01T23:00:00.000Z"
            },
            {
                "_id": "1739981967316x740735235989814300",
                "name_text": "Brief Community team",
                "approval_time_number": 1,
                "time_period_number": 2,
                "duration": 3,
                "user_contributors_list_custom_user_assignment": [
                    "1739981967495x666620907391254800"
                ],
                "contributors": [
                    "1739981967495x666620907391254800"
                ],
                "authorisers": [
                    "1739981967518x604236148329961200"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739981968553x447576109416792060",
                        "source_id": "1739981965976x489988335397975000",
                        "target_id": "1739981967316x740735235989814300",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739981965976x489988335397975000",
                            "name_text": "UA/Messaging Schedule",
                            "start": "2025-06-10T23:00:00.000Z",
                            "end": "2025-06-14T23:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739981968779x965837293557641600",
                        "source_id": "1739981965717x581562804154502900",
                        "target_id": "1739981967316x740735235989814300",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739981965717x581562804154502900",
                            "name_text": "Coms  Preparation",
                            "start": "2025-06-05T23:00:00.000Z",
                            "end": "2025-06-09T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-06-15T23:00:00.000Z",
                "end": "2025-06-18T23:00:00.000Z"
            },
            {
                "_id": "1739981966926x196270568840354340",
                "name_text": "Ops Team Briefing",
                "approval_time_number": 1,
                "time_period_number": 2,
                "duration": 3,
                "user_contributors_list_custom_user_assignment": [
                    "1739981967263x909131006406031000"
                ],
                "contributors": [
                    "1739981967263x909131006406031000"
                ],
                "authorisers": [
                    "1739981967366x397314933715351900"
                ],
                "position": 1,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739981969072x730646767808773800",
                        "source_id": "1739981965462x827481020790630900",
                        "target_id": "1739981966926x196270568840354340",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739981965462x827481020790630900",
                            "name_text": "Deployment to Staging",
                            "start": "2025-05-21T23:00:00.000Z",
                            "end": "2025-05-27T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-06-15T23:00:00.000Z",
                "end": "2025-06-18T23:00:00.000Z"
            },
            {
                "_id": "1739981966441x354559646013669500",
                "name_text": "Deployment to Live",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1739981966936x274342744616624680"
                ],
                "contributors": [
                    "1739981966936x274342744616624680"
                ],
                "authorisers": [
                    "1739981967228x424628346042624600"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739981969389x105446721987030300",
                        "source_id": "1739981965462x827481020790630900",
                        "target_id": "1739981966441x354559646013669500",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739981965462x827481020790630900",
                            "name_text": "Deployment to Staging",
                            "start": "2025-05-21T23:00:00.000Z",
                            "end": "2025-05-27T23:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739981969548x964674548995494100",
                        "source_id": "1739981966926x196270568840354340",
                        "target_id": "1739981966441x354559646013669500",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739981966926x196270568840354340",
                            "name_text": "Ops Team Briefing",
                            "start": "2025-06-15T23:00:00.000Z",
                            "end": "2025-06-18T23:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739981969662x791266504370492700",
                        "source_id": "1739981967316x740735235989814300",
                        "target_id": "1739981966441x354559646013669500",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739981967316x740735235989814300",
                            "name_text": "Brief Community team",
                            "start": "2025-06-15T23:00:00.000Z",
                            "end": "2025-06-18T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": false,
                "is_fixed": false,
                "start": "2025-06-19T23:00:00.000Z",
                "end": "2025-06-23T23:00:00.000Z"
            },
            {
                "_id": "1739981965976x489988335397975000",
                "name_text": "UA/Messaging Schedule",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1739981966394x180375429238458940"
                ],
                "contributors": [
                    "1739981966394x180375429238458940"
                ],
                "authorisers": [
                    "1739981966402x347190364181494200"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739981969873x840075380658377300",
                        "source_id": "1739981965717x581562804154502900",
                        "target_id": "1739981965976x489988335397975000",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739981965717x581562804154502900",
                            "name_text": "Coms  Preparation",
                            "start": "2025-06-05T23:00:00.000Z",
                            "end": "2025-06-09T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-06-10T23:00:00.000Z",
                "end": "2025-06-14T23:00:00.000Z"
            },
            {
                "_id": "1739981965717x581562804154502900",
                "name_text": "Coms  Preparation",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1739981965898x274103102912695230"
                ],
                "contributors": [
                    "1739981965898x274103102912695230"
                ],
                "authorisers": [
                    "1739981965870x965533193861154400"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739981970045x632193036518280800",
                        "source_id": "1739981963836x624055400747995600",
                        "target_id": "1739981965717x581562804154502900",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739981963836x624055400747995600",
                            "name_text": "Communication Strategy",
                            "start": "2025-05-19T23:00:00.000Z",
                            "end": "2025-05-23T23:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739981970140x146052360802924060",
                        "source_id": "1739981965170x145026597530497400",
                        "target_id": "1739981965717x581562804154502900",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739981965170x145026597530497400",
                            "name_text": "Marketing Assets",
                            "start": "2025-05-28T23:00:00.000Z",
                            "end": "2025-06-04T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-06-05T23:00:00.000Z",
                "end": "2025-06-09T23:00:00.000Z"
            },
            {
                "_id": "1739981965462x827481020790630900",
                "name_text": "Deployment to Staging",
                "approval_time_number": 1,
                "time_period_number": 5,
                "duration": 6,
                "user_contributors_list_custom_user_assignment": [
                    "1739981965696x305055147362799360"
                ],
                "contributors": [
                    "1739981965696x305055147362799360"
                ],
                "authorisers": [
                    "1739981965643x495398640914232100"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739981970311x975196325442292000",
                        "source_id": "1739981964103x943661562472240000",
                        "target_id": "1739981965462x827481020790630900",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739981964103x943661562472240000",
                            "name_text": "QA Testing",
                            "start": "2025-05-13T23:00:00.000Z",
                            "end": "2025-05-20T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": false,
                "is_fixed": false,
                "priority_option": "Critical (P1)",
                "start": "2025-05-21T23:00:00.000Z",
                "end": "2025-05-27T23:00:00.000Z"
            },
            {
                "_id": "1739981965170x145026597530497400",
                "name_text": "Marketing Assets",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1739981965451x499880684443022800"
                ],
                "contributors": [
                    "1739981965451x499880684443022800"
                ],
                "authorisers": [
                    "1739981965877x905566640325286400"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739981970511x858890733646448000",
                        "source_id": "1739981963836x624055400747995600",
                        "target_id": "1739981965170x145026597530497400",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739981963836x624055400747995600",
                            "name_text": "Communication Strategy",
                            "start": "2025-05-19T23:00:00.000Z",
                            "end": "2025-05-23T23:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739981970612x279192807075447900",
                        "source_id": "1739981963054x704172761114640900",
                        "target_id": "1739981965170x145026597530497400",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739981963054x704172761114640900",
                            "name_text": "Mission Config",
                            "start": "2025-05-19T23:00:00.000Z",
                            "end": "2025-05-23T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-05-28T23:00:00.000Z",
                "end": "2025-06-04T23:00:00.000Z"
            },
            {
                "_id": "1739981964619x842018408200052100",
                "name_text": "In-Game Shop Config",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1739981965129x528126983825948800"
                ],
                "contributors": [
                    "1739981965129x528126983825948800"
                ],
                "authorisers": [
                    "1739981965105x880900389967123300"
                ],
                "position": 1,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739981971006x902643907691083000",
                        "source_id": "1739981963054x704172761114640900",
                        "target_id": "1739981964619x842018408200052100",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739981963054x704172761114640900",
                            "name_text": "Mission Config",
                            "start": "2025-05-19T23:00:00.000Z",
                            "end": "2025-05-23T23:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739981971239x146212868771629080",
                        "source_id": "1739981963499x920649381267218300",
                        "target_id": "1739981964619x842018408200052100",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739981963499x920649381267218300",
                            "name_text": "Shop Pricing",
                            "start": "2025-05-19T23:00:00.000Z",
                            "end": "2025-05-23T23:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739981971558x364437112303501500",
                        "source_id": "1739981963836x624055400747995600",
                        "target_id": "1739981964619x842018408200052100",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739981963836x624055400747995600",
                            "name_text": "Communication Strategy",
                            "start": "2025-05-19T23:00:00.000Z",
                            "end": "2025-05-23T23:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739981971671x374999421562138400",
                        "source_id": "1739981964619x842018408200052100",
                        "target_id": "1739981966441x354559646013669500",
                        "type": "successor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739981966441x354559646013669500",
                            "name_text": "Deployment to Live",
                            "start": "2025-06-19T23:00:00.000Z",
                            "end": "2025-06-23T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-05-24T23:00:00.000Z",
                "end": "2025-05-28T23:00:00.000Z"
            },
            {
                "_id": "1739981964103x943661562472240000",
                "name_text": "QA Testing",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1739981964347x717909927064497900"
                ],
                "contributors": [
                    "1739981964347x717909927064497900"
                ],
                "authorisers": [
                    "1739981964412x605927819026763000"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739981971894x702320387911348000",
                        "source_id": "1739981961830x560881427463203000",
                        "target_id": "1739981964103x943661562472240000",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739981961830x560881427463203000",
                            "name_text": "Environment Art",
                            "start": "2025-05-05T23:00:00.000Z",
                            "end": "2025-05-12T23:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739981972081x693755642663405700",
                        "source_id": "1739981962712x643279928703357700",
                        "target_id": "1739981964103x943661562472240000",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739981962712x643279928703357700",
                            "name_text": "Game Mode Config",
                            "start": "2025-05-14T23:00:00.000Z",
                            "end": "2025-05-18T23:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739981972275x472125165488791700",
                        "source_id": "1739981962406x877691278227382500",
                        "target_id": "1739981964103x943661562472240000",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739981962406x877691278227382500",
                            "name_text": "Character Cosmetics",
                            "start": "2025-05-05T23:00:00.000Z",
                            "end": "2025-05-12T23:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1739981972408x233152798156153200",
                        "source_id": "1739981962142x145567681766792350",
                        "target_id": "1739981964103x943661562472240000",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739981962142x145567681766792350",
                            "name_text": "Easter Audio Update",
                            "start": "2025-05-05T23:00:00.000Z",
                            "end": "2025-05-12T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": false,
                "is_fixed": false,
                "priority_option": "Critical (P1)",
                "start": "2025-05-13T23:00:00.000Z",
                "end": "2025-05-20T23:00:00.000Z"
            },
            {
                "_id": "1739981963836x624055400747995600",
                "name_text": "Communication Strategy",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1739981964130x732321267539548900"
                ],
                "contributors": [
                    "1739981964130x732321267539548900"
                ],
                "authorisers": [
                    "1739981964083x447585053109560900"
                ],
                "position": 1,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739981972632x111701883879501200",
                        "source_id": "1739981961540x330980778415825840",
                        "target_id": "1739981963836x624055400747995600",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739981961540x330980778415825840",
                            "name_text": "Concept Design",
                            "start": "2025-04-27T23:00:00.000Z",
                            "end": "2025-05-04T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-05-19T23:00:00.000Z",
                "end": "2025-05-23T23:00:00.000Z"
            },
            {
                "_id": "1739981963499x920649381267218300",
                "name_text": "Shop Pricing",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1739981963787x334301191196950200"
                ],
                "contributors": [
                    "1739981963787x334301191196950200"
                ],
                "authorisers": [
                    "1739981963800x805257468134139500"
                ],
                "position": 2,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739981972828x432678341973860000",
                        "source_id": "1739981961540x330980778415825840",
                        "target_id": "1739981963499x920649381267218300",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739981961540x330980778415825840",
                            "name_text": "Concept Design",
                            "start": "2025-04-27T23:00:00.000Z",
                            "end": "2025-05-04T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-05-19T23:00:00.000Z",
                "end": "2025-05-23T23:00:00.000Z"
            },
            {
                "_id": "1739981963054x704172761114640900",
                "name_text": "Mission Config",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1739981963421x527296675471422600"
                ],
                "contributors": [
                    "1739981963421x527296675471422600"
                ],
                "authorisers": [
                    "1739981963352x835540019242380800"
                ],
                "position": 3,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739981973037x166787233302048140",
                        "source_id": "1739981962712x643279928703357700",
                        "target_id": "1739981963054x704172761114640900",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739981962712x643279928703357700",
                            "name_text": "Game Mode Config",
                            "start": "2025-05-14T23:00:00.000Z",
                            "end": "2025-05-18T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-05-19T23:00:00.000Z",
                "end": "2025-05-23T23:00:00.000Z"
            },
            {
                "_id": "1739981962712x643279928703357700",
                "name_text": "Game Mode Config",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1739981963031x855335422803685100"
                ],
                "contributors": [
                    "1739981963031x855335422803685100"
                ],
                "authorisers": [
                    "1739981962975x102866769733817470"
                ],
                "position": 3,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739981973222x423317170640575000",
                        "source_id": "1739981961540x330980778415825840",
                        "target_id": "1739981962712x643279928703357700",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739981961540x330980778415825840",
                            "name_text": "Concept Design",
                            "start": "2025-04-27T23:00:00.000Z",
                            "end": "2025-05-04T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-05-14T23:00:00.000Z",
                "end": "2025-05-18T23:00:00.000Z"
            },
            {
                "_id": "1739981962406x877691278227382500",
                "name_text": "Character Cosmetics",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1739981962748x928811370405549400"
                ],
                "contributors": [
                    "1739981962748x928811370405549400"
                ],
                "authorisers": [
                    "1739981962635x136701612888210370"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739981973405x946636178819437600",
                        "source_id": "1739981961540x330980778415825840",
                        "target_id": "1739981962406x877691278227382500",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739981961540x330980778415825840",
                            "name_text": "Concept Design",
                            "start": "2025-04-27T23:00:00.000Z",
                            "end": "2025-05-04T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-05-05T23:00:00.000Z",
                "end": "2025-05-12T23:00:00.000Z"
            },
            {
                "_id": "1739981962142x145567681766792350",
                "name_text": "Easter Audio Update",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1739981962438x498939869506041000"
                ],
                "contributors": [
                    "1739981962438x498939869506041000"
                ],
                "authorisers": [
                    "1739981962366x301343554599915480"
                ],
                "position": 1,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739981974026x601216516185651600",
                        "source_id": "1739981961540x330980778415825840",
                        "target_id": "1739981962142x145567681766792350",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739981961540x330980778415825840",
                            "name_text": "Concept Design",
                            "start": "2025-04-27T23:00:00.000Z",
                            "end": "2025-05-04T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-05-05T23:00:00.000Z",
                "end": "2025-05-12T23:00:00.000Z"
            },
            {
                "_id": "1739981961830x560881427463203000",
                "name_text": "Environment Art",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1739981962205x418505776071024450"
                ],
                "contributors": [
                    "1739981962205x418505776071024450"
                ],
                "authorisers": [
                    "1739981962053x115447558789162130"
                ],
                "position": 2,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1739981974209x128634917500684450",
                        "source_id": "1739981961540x330980778415825840",
                        "target_id": "1739981961830x560881427463203000",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1739981961540x330980778415825840",
                            "name_text": "Concept Design",
                            "start": "2025-04-27T23:00:00.000Z",
                            "end": "2025-05-04T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "start": "2025-05-05T23:00:00.000Z",
                "end": "2025-05-12T23:00:00.000Z"
            },
            {
                "_id": "1739981961540x330980778415825840",
                "name_text": "Concept Design",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1742131290410x399870511191687200",
                    "1742172349460x409927558840975360",
                    "1742407770523x905563495617003500"
                ],
                "contributors": [
                    "1742131290410x399870511191687200",
                    "1742172349460x409927558840975360",
                    "1742407770523x905563495617003500"
                ],
                "authorisers": [],
                "position": 0,
                "dependencies_list_custom_dependency": [],
                "is_parallel": false,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-04-27T23:00:00.000Z",
                "end": "2025-05-04T23:00:00.000Z"
            }
        ],
        "dependencies": [
            {
                "_id": "1740578706599x574532286803607550",
                "source_id": "1737299532689x151851497938223100",
                "target_id": "1739981930984x434334345026863100",
                "type": "predecessor",
                "is_dependent_into": true,
                "data_type": "activity",
                "obj": {
                    "name_text": "Store pricing event",
                    "start_date_date": "2025-01-24T00:00:00.000Z",
                    "approval_time_number": 2,
                    "workstartdate_date": "2025-01-07T00:00:00.000Z",
                    "workenddate_date": "2025-01-22T00:00:00.000Z",
                    "_id": "1737299532689x151851497938223100"
                }
            }
        ],
        "work_start_date": "2025-04-17T23:00:00.000Z",
        "work_end_date": "2025-07-01T23:00:00.000Z",
        "authorisers": [
            "1740578757277x380287898536902660"
        ],
        "deliverable_authorisers": [
            "1739981967747x407208325065166600",
            "1739981967518x604236148329961200",
            "1739981967366x397314933715351900",
            "1739981967228x424628346042624600",
            "1739981966402x347190364181494200",
            "1739981965870x965533193861154400",
            "1739981965643x495398640914232100",
            "1739981965877x905566640325286400",
            "1739981965105x880900389967123300",
            "1739981964412x605927819026763000",
            "1739981964083x447585053109560900",
            "1739981963800x805257468134139500",
            "1739981963352x835540019242380800",
            "1739981962975x102866769733817470",
            "1739981962635x136701612888210370",
            "1739981962366x301343554599915480",
            "1739981962053x115447558789162130"
        ],
        "deliverable_contributors": [
            "1739981967854x388818745902505400",
            "1739981967495x666620907391254800",
            "1739981967263x909131006406031000",
            "1739981966936x274342744616624680",
            "1739981966394x180375429238458940",
            "1739981965898x274103102912695230",
            "1739981965696x305055147362799360",
            "1739981965451x499880684443022800",
            "1739981965129x528126983825948800",
            "1739981964347x717909927064497900",
            "1739981964130x732321267539548900",
            "1739981963787x334301191196950200",
            "1739981963421x527296675471422600",
            "1739981963031x855335422803685100",
            "1739981962748x928811370405549400",
            "1739981962438x498939869506041000",
            "1739981962205x418505776071024450",
            "1742131290410x399870511191687200",
            "1742172349460x409927558840975360",
            "1742407770523x905563495617003500"
        ]
    },
    {
        "_id": "1741007954798x615360299303436300",
        "name_text": "Harvest Festival ",
        "approval_time_number": 2,
        "start_date_date": "2025-08-31T23:00:00.000Z",
        "end_date_date": "2025-09-29T23:00:00.000Z",
        "activity_creator_user": "1727366821155x669597436804614000",
        "deliverables_list_custom_deliverable2": [
            {
                "_id": "1741008023733x966503052889255300",
                "name_text": "Pre-Launch Coms",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1741008023978x327152936669927300"
                ],
                "contributors": [
                    "1741008023978x327152936669927300"
                ],
                "authorisers": [
                    "1741008023898x983431283250205200"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-07-22T23:00:00.000Z",
                "end": "2025-07-29T23:00:00.000Z"
            },
            {
                "_id": "1741008023432x130179155116010530",
                "name_text": "Brief Community team",
                "approval_time_number": 1,
                "time_period_number": 2,
                "duration": 3,
                "user_contributors_list_custom_user_assignment": [
                    "1741008023840x774263558740846500"
                ],
                "contributors": [
                    "1741008023840x774263558740846500"
                ],
                "authorisers": [
                    "1741008023634x120388397187878080"
                ],
                "position": 1,
                "dependencies_list_custom_dependency": [],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-07-13T23:00:00.000Z",
                "end": "2025-07-16T23:00:00.000Z"
            },
            {
                "_id": "1741008023061x742009158696702300",
                "name_text": "Ops Team Briefing",
                "approval_time_number": 1,
                "time_period_number": 2,
                "duration": 3,
                "user_contributors_list_custom_user_assignment": [
                    "1741008023358x292906685474240100"
                ],
                "contributors": [
                    "1741008023358x292906685474240100"
                ],
                "authorisers": [
                    "1741008023438x668830207503227300"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-07-13T23:00:00.000Z",
                "end": "2025-07-16T23:00:00.000Z"
            },
            {
                "_id": "1741008022559x205790065176930200",
                "name_text": "Deployment to Live",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1741008022949x448265724748065300"
                ],
                "contributors": [
                    "1741008022949x448265724748065300"
                ],
                "authorisers": [
                    "1741008022936x521731256979330600"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [],
                "is_parallel": false,
                "is_fixed": false,
                "start": "2025-07-17T23:00:00.000Z",
                "end": "2025-07-21T23:00:00.000Z"
            },
            {
                "_id": "1741008022160x371404218271348160",
                "name_text": "UA/Messaging Schedule",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1741008022378x684840057602694900"
                ],
                "contributors": [
                    "1741008022378x684840057602694900"
                ],
                "authorisers": [
                    "1741008022364x581572130940203500"
                ],
                "position": 3,
                "dependencies_list_custom_dependency": [],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-07-25T23:00:00.000Z",
                "end": "2025-07-29T23:00:00.000Z"
            },
            {
                "_id": "1741008021919x238569300117959170",
                "name_text": "Coms  Preparation",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1741008022161x273771480989453600"
                ],
                "contributors": [
                    "1741008022161x273771480989453600"
                ],
                "authorisers": [
                    "1741008022144x936292459243620100"
                ],
                "position": 2,
                "dependencies_list_custom_dependency": [],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-07-25T23:00:00.000Z",
                "end": "2025-07-29T23:00:00.000Z"
            },
            {
                "_id": "1741008021674x724769228468098700",
                "name_text": "Deployment to Staging",
                "approval_time_number": 1,
                "time_period_number": 5,
                "duration": 6,
                "user_contributors_list_custom_user_assignment": [
                    "1741008021898x706930890051145500"
                ],
                "contributors": [
                    "1741008021898x706930890051145500"
                ],
                "authorisers": [
                    "1741008021872x908087912346483300"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [],
                "is_parallel": false,
                "is_fixed": false,
                "priority_option": "Critical (P1)",
                "start": "2025-08-15T23:00:00.000Z",
                "end": "2025-08-21T23:00:00.000Z"
            },
            {
                "_id": "1741008021425x965388630825831400",
                "name_text": "Marketing Assets",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1741008021650x857852433749589400"
                ],
                "contributors": [
                    "1741008021650x857852433749589400"
                ],
                "authorisers": [
                    "1741008021631x721117382957607800"
                ],
                "position": 5,
                "dependencies_list_custom_dependency": [],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-07-22T23:00:00.000Z",
                "end": "2025-07-29T23:00:00.000Z"
            },
            {
                "_id": "1741008021014x432286736471432900",
                "name_text": "In-Game Shop Config",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1741008021502x345377203680861760"
                ],
                "contributors": [
                    "1741008021502x345377203680861760"
                ],
                "authorisers": [
                    "1741008021264x569799377369277600"
                ],
                "position": 7,
                "dependencies_list_custom_dependency": [],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-07-25T23:00:00.000Z",
                "end": "2025-07-29T23:00:00.000Z"
            },
            {
                "_id": "1741008020757x993586969664920800",
                "name_text": "QA Testing",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1741008020948x965183864084292500"
                ],
                "contributors": [
                    "1741008020948x965183864084292500"
                ],
                "authorisers": [
                    "1741008020938x708477954254391760"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [],
                "is_parallel": false,
                "is_fixed": false,
                "priority_option": "Critical (P1)",
                "start": "2025-08-22T23:00:00.000Z",
                "end": "2025-08-29T23:00:00.000Z"
            },
            {
                "_id": "1741008020368x671440275493907700",
                "name_text": "Communication Strategy",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1741008020699x507158080009697300"
                ],
                "contributors": [
                    "1741008020699x507158080009697300"
                ],
                "authorisers": [
                    "1741008020676x349769065495857600"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1742480535218x148544227003596800",
                        "source_id": "1741008018012x480356515151932700",
                        "target_id": "1741008020368x671440275493907700",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1741008018012x480356515151932700",
                            "name_text": "Concept Design",
                            "start": "2025-07-30T23:00:00.000Z",
                            "end": "2025-08-06T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-08-10T23:00:00.000Z",
                "end": "2025-08-14T23:00:00.000Z"
            },
            {
                "_id": "1741008020076x415419366138209100",
                "name_text": "Shop Pricing",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1741008020319x235283836113217470"
                ],
                "contributors": [
                    "1741008020319x235283836113217470"
                ],
                "authorisers": [
                    "1741008020305x522801645775608800"
                ],
                "position": 6,
                "dependencies_list_custom_dependency": [],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-07-25T23:00:00.000Z",
                "end": "2025-07-29T23:00:00.000Z"
            },
            {
                "_id": "1741008019721x842484647566649500",
                "name_text": "Mission Config",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1741008019983x444893427230612400"
                ],
                "contributors": [
                    "1741008019983x444893427230612400"
                ],
                "authorisers": [
                    "1741008019988x348005477495172600"
                ],
                "position": 1,
                "dependencies_list_custom_dependency": [],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-07-25T23:00:00.000Z",
                "end": "2025-07-29T23:00:00.000Z"
            },
            {
                "_id": "1741008019295x768657827296942800",
                "name_text": "Game Mode Config",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1741008019589x285146703578850850"
                ],
                "contributors": [
                    "1741008019589x285146703578850850"
                ],
                "authorisers": [
                    "1741008019548x606240591433119500"
                ],
                "position": 1,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1742480497439x410492857566101500",
                        "source_id": "1741008018012x480356515151932700",
                        "target_id": "1741008019295x768657827296942800",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1741008018012x480356515151932700",
                            "name_text": "Concept Design",
                            "start": "2025-07-30T23:00:00.000Z",
                            "end": "2025-08-06T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-08-10T23:00:00.000Z",
                "end": "2025-08-14T23:00:00.000Z"
            },
            {
                "_id": "1741008018933x516042433329566600",
                "name_text": "Character Cosmetics",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1741008019150x592396550104742500"
                ],
                "contributors": [
                    "1741008019150x592396550104742500"
                ],
                "authorisers": [
                    "1741008019150x275618354102068480"
                ],
                "position": 2,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1742480484794x500349999140306940",
                        "source_id": "1741008018012x480356515151932700",
                        "target_id": "1741008018933x516042433329566600",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1741008018012x480356515151932700",
                            "name_text": "Concept Design",
                            "start": "2025-07-30T23:00:00.000Z",
                            "end": "2025-08-06T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-08-07T23:00:00.000Z",
                "end": "2025-08-14T23:00:00.000Z"
            },
            {
                "_id": "1741008018673x548391867583029500",
                "name_text": "August  Audio Update",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1741008018881x329648863292982850"
                ],
                "contributors": [
                    "1741008018881x329648863292982850"
                ],
                "authorisers": [
                    "1741008018838x255905095034179900"
                ],
                "position": 3,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1742480472243x687804467125944300",
                        "source_id": "1741008018012x480356515151932700",
                        "target_id": "1741008018673x548391867583029500",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1741008018012x480356515151932700",
                            "name_text": "Concept Design",
                            "start": "2025-07-30T23:00:00.000Z",
                            "end": "2025-08-06T23:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-08-07T23:00:00.000Z",
                "end": "2025-08-14T23:00:00.000Z"
            },
            {
                "_id": "1741008018354x151702909718828060",
                "name_text": "Environment Art",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1741008018628x959793929714363000"
                ],
                "contributors": [
                    "1741008018628x959793929714363000"
                ],
                "authorisers": [
                    "1741008018624x769441410809258600"
                ],
                "position": 4,
                "dependencies_list_custom_dependency": [],
                "is_parallel": true,
                "is_fixed": false,
                "start": "2025-07-22T23:00:00.000Z",
                "end": "2025-07-29T23:00:00.000Z"
            },
            {
                "_id": "1741008018012x480356515151932700",
                "name_text": "Concept Design",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [],
                "contributors": [],
                "authorisers": [],
                "position": 0,
                "dependencies_list_custom_dependency": [],
                "is_parallel": false,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-07-30T23:00:00.000Z",
                "end": "2025-08-06T23:00:00.000Z"
            }
        ],
        "dependencies": [
            {
                "_id": "1743003947145x930288219479343100",
                "source_id": "1742481898617x446495001186664450",
                "target_id": "1741007954798x615360299303436300",
                "type": "predecessor",
                "is_dependent_into": true,
                "data_type": "activity",
                "obj": {
                    "name_text": "Server Update",
                    "start_date_date": "2025-06-29T23:00:00.000Z",
                    "approval_time_number": 2,
                    "workstartdate_date": "2025-06-24T23:00:00.000Z",
                    "workenddate_date": "2025-06-27T23:00:00.000Z",
                    "_id": "1742481898617x446495001186664450"
                }
            }
        ],
        "work_start_date": "2025-07-13T23:00:00.000Z",
        "work_end_date": "2025-08-29T23:00:00.000Z",
        "authorisers": [
            "1742480027940x872027470541094900"
        ],
        "deliverable_authorisers": [
            "1741008023898x983431283250205200",
            "1741008023634x120388397187878080",
            "1741008023438x668830207503227300",
            "1741008022936x521731256979330600",
            "1741008022364x581572130940203500",
            "1741008022144x936292459243620100",
            "1741008021872x908087912346483300",
            "1741008021631x721117382957607800",
            "1741008021264x569799377369277600",
            "1741008020938x708477954254391760",
            "1741008020676x349769065495857600",
            "1741008020305x522801645775608800",
            "1741008019988x348005477495172600",
            "1741008019548x606240591433119500",
            "1741008019150x275618354102068480",
            "1741008018838x255905095034179900",
            "1741008018624x769441410809258600"
        ],
        "deliverable_contributors": [
            "1741008023978x327152936669927300",
            "1741008023840x774263558740846500",
            "1741008023358x292906685474240100",
            "1741008022949x448265724748065300",
            "1741008022378x684840057602694900",
            "1741008022161x273771480989453600",
            "1741008021898x706930890051145500",
            "1741008021650x857852433749589400",
            "1741008021502x345377203680861760",
            "1741008020948x965183864084292500",
            "1741008020699x507158080009697300",
            "1741008020319x235283836113217470",
            "1741008019983x444893427230612400",
            "1741008019589x285146703578850850",
            "1741008019150x592396550104742500",
            "1741008018881x329648863292982850",
            "1741008018628x959793929714363000"
        ]
    },
    {
        "_id": "1742481898617x446495001186664450",
        "name_text": "Server Update",
        "approval_time_number": 2,
        "start_date_date": "2025-06-29T23:00:00.000Z",
        "end_date_date": null,
        "activity_creator_user": "1727366821155x669597436804614000",
        "deliverables_list_custom_deliverable2": [
            {
                "_id": "1742481921725x392884693512974600",
                "name_text": "Store Pricing/ Sales Events Report",
                "approval_time_number": 1,
                "time_period_number": 2,
                "duration": 3,
                "user_contributors_list_custom_user_assignment": [],
                "contributors": [],
                "authorisers": [],
                "position": 0,
                "dependencies_list_custom_dependency": [],
                "is_parallel": false,
                "is_fixed": false,
                "start": "2025-06-24T23:00:00.000Z",
                "end": "2025-06-27T23:00:00.000Z"
            }
        ],
        "dependencies": [],
        "work_start_date": "2025-06-24T23:00:00.000Z",
        "work_end_date": "2025-06-27T23:00:00.000Z",
        "authorisers": [
            "1742481921320x809467936526358700",
            "1742481921727x432369101745160200"
        ],
        "deliverable_authorisers": [],
        "deliverable_contributors": []
    },
    {
        "_id": "1742932940997x384993072090972160",
        "name_text": "Hello world",
        "approval_time_number": 2,
        "start_date_date": "2025-03-26T00:00:00.000Z",
        "end_date_date": null,
        "activity_creator_user": "1736774776172x146105203190919360",
        "deliverables_list_custom_deliverable2": [
            {
                "_id": "1742932970035x434500684612186200",
                "name_text": "Pre-Launch Coms",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1742932970485x766619098329052800"
                ],
                "contributors": [
                    "1742932970485x766619098329052800"
                ],
                "authorisers": [
                    "1742932970417x374226651996604200"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1742932982468x601890574205981700",
                        "source_id": "1742932969716x334415756897639800",
                        "target_id": "1742932970035x434500684612186200",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1742932969716x334415756897639800",
                            "name_text": "Brief Community team",
                            "start": "2025-03-08T00:00:00.000Z",
                            "end": "2025-03-11T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-03-17T00:00:00.000Z",
                "end": "2025-03-24T00:00:00.000Z"
            },
            {
                "_id": "1742932969716x334415756897639800",
                "name_text": "Brief Community team",
                "approval_time_number": 1,
                "time_period_number": 2,
                "duration": 3,
                "user_contributors_list_custom_user_assignment": [
                    "1742932970019x583787821607320000"
                ],
                "contributors": [
                    "1742932970019x583787821607320000"
                ],
                "authorisers": [
                    "1742932969973x763983089317186600"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1742932983228x456213306916852800",
                        "source_id": "1742932968161x231648592468895580",
                        "target_id": "1742932969716x334415756897639800",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1742932968161x231648592468895580",
                            "name_text": "UA/Messaging Schedule",
                            "start": "2025-03-03T00:00:00.000Z",
                            "end": "2025-03-07T00:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1742932983462x468008502211273660",
                        "source_id": "1742932967838x212165155395610300",
                        "target_id": "1742932969716x334415756897639800",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1742932967838x212165155395610300",
                            "name_text": "Coms  Preparation",
                            "start": "2025-02-26T00:00:00.000Z",
                            "end": "2025-03-02T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-03-08T00:00:00.000Z",
                "end": "2025-03-11T00:00:00.000Z"
            },
            {
                "_id": "1742932969298x612570422682165400",
                "name_text": "Ops Team Briefing",
                "approval_time_number": 1,
                "time_period_number": 2,
                "duration": 3,
                "user_contributors_list_custom_user_assignment": [
                    "1742932969560x198156253535227260"
                ],
                "contributors": [
                    "1742932969560x198156253535227260"
                ],
                "authorisers": [
                    "1742932969567x212442842786143100"
                ],
                "position": 2,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1742932984270x297988999028350200",
                        "source_id": "1742932967490x352140785555286460",
                        "target_id": "1742932969298x612570422682165400",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1742932967490x352140785555286460",
                            "name_text": "Deployment to Staging",
                            "start": "2025-02-06T00:00:00.000Z",
                            "end": "2025-02-12T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-02-14T00:00:00.000Z",
                "end": "2025-02-17T00:00:00.000Z"
            },
            {
                "_id": "1742932968808x569759207837074560",
                "name_text": "Deployment to Live",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1742932969151x832927458311314400"
                ],
                "contributors": [
                    "1742932969151x832927458311314400"
                ],
                "authorisers": [
                    "1742932969155x303757669513653000"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1742932984540x369713861263861300",
                        "source_id": "1742932967490x352140785555286460",
                        "target_id": "1742932968808x569759207837074560",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1742932967490x352140785555286460",
                            "name_text": "Deployment to Staging",
                            "start": "2025-02-06T00:00:00.000Z",
                            "end": "2025-02-12T00:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1742932984685x303718024755407700",
                        "source_id": "1742932969298x612570422682165400",
                        "target_id": "1742932968808x569759207837074560",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1742932969298x612570422682165400",
                            "name_text": "Ops Team Briefing",
                            "start": "2025-02-14T00:00:00.000Z",
                            "end": "2025-02-17T00:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1742932984896x547370663857875700",
                        "source_id": "1742932969716x334415756897639800",
                        "target_id": "1742932968808x569759207837074560",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1742932969716x334415756897639800",
                            "name_text": "Brief Community team",
                            "start": "2025-03-08T00:00:00.000Z",
                            "end": "2025-03-11T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": false,
                "is_fixed": false,
                "start": "2025-03-12T00:00:00.000Z",
                "end": "2025-03-16T00:00:00.000Z"
            },
            {
                "_id": "1742932968161x231648592468895580",
                "name_text": "UA/Messaging Schedule",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1742932968843x955866237952981200"
                ],
                "contributors": [
                    "1742932968843x955866237952981200"
                ],
                "authorisers": [
                    "1742932968501x869229811321639000"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1742932985151x502290687997811500",
                        "source_id": "1742932967838x212165155395610300",
                        "target_id": "1742932968161x231648592468895580",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1742932967838x212165155395610300",
                            "name_text": "Coms  Preparation",
                            "start": "2025-02-26T00:00:00.000Z",
                            "end": "2025-03-02T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-03-03T00:00:00.000Z",
                "end": "2025-03-07T00:00:00.000Z"
            },
            {
                "_id": "1742932967838x212165155395610300",
                "name_text": "Coms  Preparation",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1742932968142x778256174986926600"
                ],
                "contributors": [
                    "1742932968142x778256174986926600"
                ],
                "authorisers": [
                    "1742932968145x118250954950549260"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1742932985403x391736535248723460",
                        "source_id": "1742932966107x115212637859224420",
                        "target_id": "1742932967838x212165155395610300",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1742932966107x115212637859224420",
                            "name_text": "Communication Strategy",
                            "start": "2025-02-13T00:00:00.000Z",
                            "end": "2025-02-17T00:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1742932985523x963918122701882000",
                        "source_id": "1742932967155x474299201914213800",
                        "target_id": "1742932967838x212165155395610300",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1742932967155x474299201914213800",
                            "name_text": "Marketing Assets",
                            "start": "2025-02-18T00:00:00.000Z",
                            "end": "2025-02-25T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-02-26T00:00:00.000Z",
                "end": "2025-03-02T00:00:00.000Z"
            },
            {
                "_id": "1742932967490x352140785555286460",
                "name_text": "Deployment to Staging",
                "approval_time_number": 1,
                "time_period_number": 5,
                "duration": 6,
                "user_contributors_list_custom_user_assignment": [
                    "1742932967776x257547833228193100"
                ],
                "contributors": [
                    "1742932967776x257547833228193100"
                ],
                "authorisers": [
                    "1742932967816x177294312984974200"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1742932985877x451867153438186000",
                        "source_id": "1742932966590x782151979378738400",
                        "target_id": "1742932967490x352140785555286460",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1742932966590x782151979378738400",
                            "name_text": "QA Testing",
                            "start": "2025-01-29T00:00:00.000Z",
                            "end": "2025-02-05T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": false,
                "is_fixed": false,
                "priority_option": "Critical (P1)",
                "start": "2025-02-06T00:00:00.000Z",
                "end": "2025-02-12T00:00:00.000Z"
            },
            {
                "_id": "1742932967155x474299201914213800",
                "name_text": "Marketing Assets",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1742932967418x768101699598206800"
                ],
                "contributors": [
                    "1742932967418x768101699598206800"
                ],
                "authorisers": [
                    "1742932967378x792268734694749000"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1742932986694x829188378049170200",
                        "source_id": "1742932966107x115212637859224420",
                        "target_id": "1742932967155x474299201914213800",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1742932966107x115212637859224420",
                            "name_text": "Communication Strategy",
                            "start": "2025-02-13T00:00:00.000Z",
                            "end": "2025-02-17T00:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1742932986800x461855028208526500",
                        "source_id": "1742932965237x465576110187718700",
                        "target_id": "1742932967155x474299201914213800",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1742932965237x465576110187718700",
                            "name_text": "Mission Config",
                            "start": "2025-01-24T00:00:00.000Z",
                            "end": "2025-01-28T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-02-18T00:00:00.000Z",
                "end": "2025-02-25T00:00:00.000Z"
            },
            {
                "_id": "1742932966862x375144411385416500",
                "name_text": "In-Game Shop Config",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1742932967149x447927990261750140"
                ],
                "contributors": [
                    "1742932967149x447927990261750140"
                ],
                "authorisers": [
                    "1742932967145x252789502320670750"
                ],
                "position": 1,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1742932987036x593687537692260400",
                        "source_id": "1742932965237x465576110187718700",
                        "target_id": "1742932966862x375144411385416500",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1742932965237x465576110187718700",
                            "name_text": "Mission Config",
                            "start": "2025-01-24T00:00:00.000Z",
                            "end": "2025-01-28T00:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1742932987152x501443156586843300",
                        "source_id": "1742932965807x767354542629280000",
                        "target_id": "1742932966862x375144411385416500",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1742932965807x767354542629280000",
                            "name_text": "Shop Pricing",
                            "start": "2025-02-13T00:00:00.000Z",
                            "end": "2025-02-17T00:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1742932987254x635043307055543000",
                        "source_id": "1742932966107x115212637859224420",
                        "target_id": "1742932966862x375144411385416500",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1742932966107x115212637859224420",
                            "name_text": "Communication Strategy",
                            "start": "2025-02-13T00:00:00.000Z",
                            "end": "2025-02-17T00:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1742932987360x922682576968994300",
                        "source_id": "1742932966862x375144411385416500",
                        "target_id": "1742932968808x569759207837074560",
                        "type": "successor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1742932968808x569759207837074560",
                            "name_text": "Deployment to Live",
                            "start": "2025-03-12T00:00:00.000Z",
                            "end": "2025-03-16T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-02-21T00:00:00.000Z",
                "end": "2025-02-25T00:00:00.000Z"
            },
            {
                "_id": "1742932966590x782151979378738400",
                "name_text": "QA Testing",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1742932967437x720284033114561900"
                ],
                "contributors": [
                    "1742932967437x720284033114561900"
                ],
                "authorisers": [
                    "1742932967479x825597755738463500"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1742932988053x360789079912059000",
                        "source_id": "1742932963934x174181139735924540",
                        "target_id": "1742932966590x782151979378738400",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1742932963934x174181139735924540",
                            "name_text": "Environment Art",
                            "start": "2025-01-16T00:00:00.000Z",
                            "end": "2025-01-23T00:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1742932988198x247210213560760350",
                        "source_id": "1742932964910x397650634257663800",
                        "target_id": "1742932966590x782151979378738400",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1742932964910x397650634257663800",
                            "name_text": "Game Mode Config",
                            "start": "2025-01-19T00:00:00.000Z",
                            "end": "2025-01-23T00:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1742932988390x838375529956224100",
                        "source_id": "1742932964640x469956695653732700",
                        "target_id": "1742932966590x782151979378738400",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1742932964640x469956695653732700",
                            "name_text": "Character Cosmetics",
                            "start": "2025-01-16T00:00:00.000Z",
                            "end": "2025-01-23T00:00:00.000Z"
                        }
                    },
                    {
                        "_id": "1742932988565x989885994954006900",
                        "source_id": "1742932964287x454064558882509200",
                        "target_id": "1742932966590x782151979378738400",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1742932964287x454064558882509200",
                            "name_text": "Easter Audio Update",
                            "start": "2025-01-16T00:00:00.000Z",
                            "end": "2025-01-23T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": false,
                "is_fixed": false,
                "priority_option": "Critical (P1)",
                "start": "2025-01-29T00:00:00.000Z",
                "end": "2025-02-05T00:00:00.000Z"
            },
            {
                "_id": "1742932966107x115212637859224420",
                "name_text": "Communication Strategy",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1742932966555x364797814637278800"
                ],
                "contributors": [
                    "1742932966555x364797814637278800"
                ],
                "authorisers": [
                    "1742932966829x313453303554624960"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1742932988807x900736854685710000",
                        "source_id": "1742932963197x672430851644414000",
                        "target_id": "1742932966107x115212637859224420",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1742932963197x672430851644414000",
                            "name_text": "Concept Design",
                            "start": "2025-01-08T00:00:00.000Z",
                            "end": "2025-01-15T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-02-13T00:00:00.000Z",
                "end": "2025-02-17T00:00:00.000Z"
            },
            {
                "_id": "1742932965807x767354542629280000",
                "name_text": "Shop Pricing",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1742932966104x611070959768441900"
                ],
                "contributors": [
                    "1742932966104x611070959768441900"
                ],
                "authorisers": [
                    "1742932966084x577056500073050400"
                ],
                "position": 1,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1742932989057x201403051827826500",
                        "source_id": "1742932963197x672430851644414000",
                        "target_id": "1742932965807x767354542629280000",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1742932963197x672430851644414000",
                            "name_text": "Concept Design",
                            "start": "2025-01-08T00:00:00.000Z",
                            "end": "2025-01-15T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "Medium (P3)",
                "start": "2025-02-13T00:00:00.000Z",
                "end": "2025-02-17T00:00:00.000Z"
            },
            {
                "_id": "1742932965237x465576110187718700",
                "name_text": "Mission Config",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1742932965746x751475481993499000"
                ],
                "contributors": [
                    "1742932965746x751475481993499000"
                ],
                "authorisers": [
                    "1742932965730x800058452518306000"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1742932989415x974782612156188600",
                        "source_id": "1742932964910x397650634257663800",
                        "target_id": "1742932965237x465576110187718700",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1742932964910x397650634257663800",
                            "name_text": "Game Mode Config",
                            "start": "2025-01-19T00:00:00.000Z",
                            "end": "2025-01-23T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-01-24T00:00:00.000Z",
                "end": "2025-01-28T00:00:00.000Z"
            },
            {
                "_id": "1742932964910x397650634257663800",
                "name_text": "Game Mode Config",
                "approval_time_number": 1,
                "time_period_number": 3,
                "duration": 4,
                "user_contributors_list_custom_user_assignment": [
                    "1742932965323x105999235199679680"
                ],
                "contributors": [
                    "1742932965323x105999235199679680"
                ],
                "authorisers": [
                    "1742932965163x713024296908730800"
                ],
                "position": 0,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1742932989654x837462833396939400",
                        "source_id": "1742932963197x672430851644414000",
                        "target_id": "1742932964910x397650634257663800",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1742932963197x672430851644414000",
                            "name_text": "Concept Design",
                            "start": "2025-01-08T00:00:00.000Z",
                            "end": "2025-01-15T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-01-19T00:00:00.000Z",
                "end": "2025-01-23T00:00:00.000Z"
            },
            {
                "_id": "1742932964640x469956695653732700",
                "name_text": "Character Cosmetics",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1742932964869x573948294170971900"
                ],
                "contributors": [
                    "1742932964869x573948294170971900"
                ],
                "authorisers": [
                    "1742932964861x788106137740448400"
                ],
                "position": 1,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1742932989889x354266264576679900",
                        "source_id": "1742932963197x672430851644414000",
                        "target_id": "1742932964640x469956695653732700",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1742932963197x672430851644414000",
                            "name_text": "Concept Design",
                            "start": "2025-01-08T00:00:00.000Z",
                            "end": "2025-01-15T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-01-16T00:00:00.000Z",
                "end": "2025-01-23T00:00:00.000Z"
            },
            {
                "_id": "1742932964287x454064558882509200",
                "name_text": "Easter Audio Update",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1742932964623x945600206510548500"
                ],
                "contributors": [
                    "1742932964623x945600206510548500"
                ],
                "authorisers": [
                    "1742932964643x823725505268489100"
                ],
                "position": 2,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1742932990137x532292111216355460",
                        "source_id": "1742932963197x672430851644414000",
                        "target_id": "1742932964287x454064558882509200",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1742932963197x672430851644414000",
                            "name_text": "Concept Design",
                            "start": "2025-01-08T00:00:00.000Z",
                            "end": "2025-01-15T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-01-16T00:00:00.000Z",
                "end": "2025-01-23T00:00:00.000Z"
            },
            {
                "_id": "1742932963934x174181139735924540",
                "name_text": "Environment Art",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [
                    "1742932964232x376705088687852900"
                ],
                "contributors": [
                    "1742932964232x376705088687852900"
                ],
                "authorisers": [
                    "1742932964223x799090438696698100"
                ],
                "position": 3,
                "dependencies_list_custom_dependency": [
                    {
                        "_id": "1742971197240x436725522967035900",
                        "source_id": "1742932963197x672430851644414000",
                        "target_id": "1742932963934x174181139735924540",
                        "type": "predecessor",
                        "is_dependent_into": false,
                        "data_type": "deliverable",
                        "obj": {
                            "_id": "1742932963197x672430851644414000",
                            "name_text": "Concept Design",
                            "start": "2025-01-08T00:00:00.000Z",
                            "end": "2025-01-15T00:00:00.000Z"
                        }
                    }
                ],
                "is_parallel": true,
                "is_fixed": false,
                "start": "2025-01-16T00:00:00.000Z",
                "end": "2025-01-23T00:00:00.000Z"
            },
            {
                "_id": "1742932963197x672430851644414000",
                "name_text": "Concept Design",
                "approval_time_number": 2,
                "time_period_number": 5,
                "duration": 7,
                "user_contributors_list_custom_user_assignment": [],
                "contributors": [],
                "authorisers": [],
                "position": 0,
                "dependencies_list_custom_dependency": [],
                "is_parallel": false,
                "is_fixed": false,
                "priority_option": "High (P2)",
                "start": "2025-01-08T00:00:00.000Z",
                "end": "2025-01-15T00:00:00.000Z"
            }
        ],
        "dependencies": [],
        "work_start_date": "2025-01-08T00:00:00.000Z",
        "work_end_date": "2025-03-24T00:00:00.000Z",
        "authorisers": [
            "1742971051608x150520410349240320"
        ],
        "deliverable_authorisers": [
            "1742932970417x374226651996604200",
            "1742932969973x763983089317186600",
            "1742932969567x212442842786143100",
            "1742932969155x303757669513653000",
            "1742932968501x869229811321639000",
            "1742932968145x118250954950549260",
            "1742932967816x177294312984974200",
            "1742932967378x792268734694749000",
            "1742932967145x252789502320670750",
            "1742932967479x825597755738463500",
            "1742932966829x313453303554624960",
            "1742932966084x577056500073050400",
            "1742932965730x800058452518306000",
            "1742932965163x713024296908730800",
            "1742932964861x788106137740448400",
            "1742932964643x823725505268489100",
            "1742932964223x799090438696698100"
        ],
        "deliverable_contributors": [
            "1742932970485x766619098329052800",
            "1742932970019x583787821607320000",
            "1742932969560x198156253535227260",
            "1742932969151x832927458311314400",
            "1742932968843x955866237952981200",
            "1742932968142x778256174986926600",
            "1742932967776x257547833228193100",
            "1742932967418x768101699598206800",
            "1742932967149x447927990261750140",
            "1742932967437x720284033114561900",
            "1742932966555x364797814637278800",
            "1742932966104x611070959768441900",
            "1742932965746x751475481993499000",
            "1742932965323x105999235199679680",
            "1742932964869x573948294170971900",
            "1742932964623x945600206510548500",
            "1742932964232x376705088687852900"
        ]
    },
    {
        "_id": "1743182318678x430268856981782500",
        "name_text": "Current Activity",
        "approval_time_number": 0,
        "start_date_date": "2025-03-28T17:18:41.146Z",
        "end_date_date": null,
        "activity_creator_user": "1736774776172x146105203190919360",
        "deliverables_list_custom_deliverable2": [],
        "dependencies": [],
        "work_start_date": null,
        "work_end_date": null,
        "authorisers": [],
        "deliverable_authorisers": [],
        "deliverable_contributors": []
    }
]


