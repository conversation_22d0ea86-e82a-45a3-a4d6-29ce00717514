@import url('https://fonts.googleapis.com/icon?family=Material+Icons');

.mdui-textfield {
    padding-left: 0px !important;
    padding-right: 0px !important;
}

.show_focus_gp {
    visibility: visible !important;
    display: block !important;
}

#nav_sidebar_full #current_tab {
    background-color: #E8DDFF !important;
    border-radius: 8px !important;
}

#current_tab div {
    background-color: #E8DDFF !important;
    border-radius: 8px !important;
}

.form-section-left-border {
    border-right: none !important;
    border-bottom: none !important;
    border-top: none !important;
}

.mdui-chips {
    padding: 0 !important;
}

#hr {
    border-left: none !important;
    border-right: none !important;
    border-top: none !important;
}

.etr {
    border-radius: 20px 0 0 20px !important;
}

.dtr {
    border-radius: 0 20px 20px 0 !important;
    border-left: none !important;
}

.table-body {
    border-left: none !important;
    border-right: none !important;
}

#dependencySection .mdui-chip editable {
    display: none;
}

#dependencySection>div.bubble-element {}

.modal .inline-flex {
    font-weight: 400;
    font-size: 9px;
}

.hidden {
    display: none !important;
}

.custom-icons {
    display: flex;
    align-items: center;
    gap: 2px;
}

.custom-icon {
    width: 16px;
    height: 15px;
    margin-left: 5px;
    padding: 1px;
    border-radius: 50%;
    background-color: #fff;
}

span.custom-icon {
    text-align: center;
    font-size: 9px;
}

.pointer-first-part {
    display: flex;
    margin-left: 0px !important;
    gap: 5px;
    text-transform: capitalize;
    font-size: 12px;
    color: #000;
}

.pointer-first-part i {
    margin-right: 10px;
    font-size: 8px;
}

.pointer-cursor {
    cursor: pointer;
}

.text-center {
    text-align: center;
}

/* Contributor section */
.contributor.flex-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    position: relative;
}

.contributor .flex-child {
    display: flex;
    align-items: center;
    gap: 2px;
    flex: 1;
}

.contributor .flex-child:last-child {
    margin-left: 10px;
    justify-content: end;
}

.flex-child:not(:last-child) {
    margin-right: 10px;
}

.contributor .divider {
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    background: black;
    border: 0.1px dotted black;
    border-style: dashed;
    transform: translateX(-50%);
}

/* End of contributors activity section */
/* Dependency components */

.dependency {
    position: relative;
    margin: 10px;
}

.dependency button.fab {
    background-color: #D5F6E8;
    color: #000;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
    cursor: pointer;
    background: transparent;
    padding: 0px;
    transition: border 0.6s linear;
}

.dependency button.fab.amber {
    background-color: #FFD324;
}

.dependency button.fab.red {
    background-color: #FFDAD8;
}

.dependency button.fab img {
    background: transparent;
    width: auto;
    height: auto;
    margin-top: -5px;
}

.dependency.pointer-first-part button.fab {
    border-radius: 50% 0px 0px 50%;
}

.dependency.pointer-third-part button.fab {
    right: -22px;
    border-radius: 0px 50% 50% 0px;
    top: -5px;
}

.dependency button.fab.ext:hover+div.modal {
    display: block;
}

.dependency div.modal {
    position: absolute;
    bottom: 25px;
    background: #D5F6E8;
    box-shadow: 0px 2px 6px 2px rgba(0, 0, 0, 0.15);
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    border-bottom-right-radius: 12px;
    padding: 10px;
    width: fit-content;
    display: none;
}

.dependency div.modal.amber {
    background-color: #FFD324;
}

.dependency div.modal.red {
    background-color: #FFDAD8;
}

.dependency div.modal.inner-content {
    width: 100%;
    display: inline-flex;
    flex-direction: column;
}

.dependency div.modal .text-bold {
    align-self: stretch;
    color: #47464F;
    font-size: 10px;
    font-family: Lato, sans-serif;
    font-weight: 700;
    line-height: 20px;
    letter-spacing: 0.10px;
    word-wrap: break-word;
}

.dependency div.modal .text-regular {
    align-self: stretch;
    color: #47464F;
    font-size: 14px;
    font-family: Lato, sans-serif;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: 0.25px;
    word-wrap: break-word;
}

.dependency div.modal .flex-row {
    align-self: stretch;
    padding-top: 10px;
    display: inline-flex;
    justify-content: flex-start;
    align-items: center;
    gap: 8px;
}

.dependency div.modal .action-text {
    text-align: center;
    color: #64558F;
    font-size: 14px;
    font-family: Lato, sans-serif;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: 0.10px;
    word-wrap: break-word;
}

.dependency.pointer-first-part div.modal {
    left: -180;
}

.dependency.pointer-third-part div.modal {
    left: 20;
}

/* End of dependency components */

.highcharts-credits {
    display: none;
}

.timeline {
    display: flex;
    background: #FAF9FB;
    flex-direction: column;
    padding: 10px 0px 15px 0px;
    box-shadow: 0px 4px 4px 0px rgba(224, 227, 232, 1);
    border: 1px solid rgba(200, 197, 208, 1);
    border-radius: 18px;
    background: #FAF9FB;
}

.viewMode {
    border-radius: 20px 20px 0px 0px !important;
    border: 1px 0px 0px 0px;
    min-height: 380px;
}

.sideSheetMode {
    max-height: 263px;
    min-height: 263px;
}

.defaultMode {
    min-height: 440px !important;
    max-height: 440px !important;
}

.defaultMode .nav-container {
    display: flex;
}

.flex-container {
    display: flex;
}

.timeline .flex-container {
    padding: 0px 15px;
    display: none;
}

.defaultMode.timeline>.flex-container {
    display: flex;
    justify-content: center;
    align-items: stretch;
    border: none;
}

section.timeline>.flex-container>nav {
    flex: 1;
    align-items: center;
    border: none;
}

section.timeline .flex-container>div {
    flex: 1;
    align-items: center;
    border: none;
}

section.timeline .flex-container>div.scroll {
    flex: 0 0 60%;
    border-left: 1px solid #C8C5D0;
    border-right: 1px solid #C8C5D0;
}

section.timeline .flex-container>*:nth-child(3) {
    border-right: 1px solid #C8C5D0;
    align-items: center;
    display: flex;
}


.justify-content-space-between {
    justify-content: space-between;
}

.bread-crumbs a:link {
    text-decoration: none;
}

.breadcrumbs {
    list-style: none;
    padding: 0;
    margin: 0;
}

.breadcrumbs__item {
    float: left;
    position: relative;
}

.breadcrumbs__link {
    display: block;
    padding: 6px 16px;
    text-decoration: none;
    color: #000;
    border-radius: 8px;
    border: 1px solid #787680;
    text-align: center;
    font-family: Lato;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: 0.1px;
    text-transform: capitalize;
}

.breadcrumbs__item:last-child {
    cursor: text;
}


#toggle-show-dependency {
    background: #D5E3FF;
    border-radius: 100px;
    border: none;
    padding: 6px 60px;
    text-align: center;
    color: #001B3B;
    font-size: 14px;
    font-family: Lato;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: 0.10px;
    word-wrap: break-word;
    text-decoration: none;
    height: fit-content;
}

.hidden {
    display: none;
}

.custom-point-1 {
    fill: #cebdfe;
    stroke: rgba(33, 33, 33, 1);
    stroke-width: 1;
    border-radius: 15px;
}

#reduce-zindex {
    z-index: 1500 !important;
}

.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    font-size: 1.5em;
    color: #555;
}

#container {
    position: relative;
}

#error-message {
    display: none;
    color: red;
    font-weight: bold;
}

.loader {
    width: 60px;
    aspect-ratio: 1;
    border: 15px solid #ddd;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(45deg);
}

.loader::before {
    content: "";
    position: absolute;
    inset: -15px;
    border-radius: 50%;
    border: 15px solid #514b82;
    animation: l18 2s infinite linear;
}

@keyframes l18 {
    0% {
        clip-path: polygon(50% 50%, 0 0, 0 0, 0 0, 0 0, 0 0);
    }

    25% {
        clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 0, 100% 0, 100% 0);
    }

    50% {
        clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 100% 100%, 100% 100%);
    }

    75% {
        clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 100%);
    }

    100% {
        clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 0);
    }
}

section.timeline {
    width: 100%;
    position: relative;
    overflow: hidden;
}

/* Loading overlay styling - Hidden by default */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.5s ease;
    z-index: 10;
}

/* Class to make overlay visible */
.loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Loading GIF (optional, if you want to add some styling) */
.loading-overlay img {
    max-width: 150px;
    max-height: 150px;
}

.error-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #ffdad88a;
    display: flex;
    justify-content: center;
    align-items: start;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.5s ease;
    z-index: 10;
    border: 1px solid #BA1A1A;
    border-radius: 20px;
}

.timeline.error .error-overlay {
    opacity: 1;
    visibility: visible;
}

.error-overlay div {
    flex: 1;
}

.error-overlay .dialog {
    background: rgba(255, 218, 214, 1);
    backdrop-filter: blur(4px);
    box-shadow: 2px 2px 2px 0px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(120, 118, 128, 1);
    width: 80%;
    min-height: 60%;
    padding: 30px;
    border-radius: 28px;
    margin-top: 60px;
    flex: 0 0 80%;
}

.error-overlay .dialog h5.headline {
    font-family: 'Lato';
    font-size: 22px;
    font-weight: 400;
    line-height: 28px;
    text-decoration-skip-ink: none;
}

.error-overlay .dialog h6 {
    font-family: 'Lato';
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    text-decoration-skip-ink: none;
}

.error-overlay .dialog h6::after {
    content: '';
    display: block;
    height: 1px;
    background-color: #787680;
    margin: 15px 0px;
}

.error-overlay .dialog ul.error_messages {
    list-style-type: none;
    margin: 0;
    padding: 0px;
}

.error-overlay .dialog ul.error_messages li {
    font-size: 16px;
    color: #181C20;
    font-family: 'Lato';
}

.error-overlay .dialog ul.error_messages li::after {
    content: '';
    display: block;
    height: 1px;
    background-color: #787680;
    margin: 15px 0px;
}

.fsmodal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100vh !important;
    justify-content: center !important;
    align-items: center !important;
    z-index: 1000;
}

.fsmodal-content {
    background-color: #fff;
    width: 100%;
    min-height: 100vh !important;
    padding: 20px;
    box-shadow: none;
    overflow-y: auto;
    border: none;
    text-align: center;
}

/* Navigation Container */
.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0px 22px;
    font-size: 14px;
    position: absolute;
    bottom: 10px;
    left: 0;
    right: 0;
    z-index: 5;
}

/* Hide nav-container in sideSheetMode */
section.sideSheetMode .nav-container {
    display: none;
}
/* Navigation Buttons */
.nav-buttons {
    display: flex;
    align-items: center;
    gap: 8px;
}

.right-buttons {
    margin-left: auto;
}

.nav-btn {
    background-color: #fff;
    border: 1px solid #d0d0d0;
    border-radius: 4px;
    padding: 6px 12px;
    color: #333;
    cursor: pointer;
    font-family: Lato, sans-serif;
    font-size: 14px;
    font-weight: 400;
}

.nav-btn:hover {
    background-color: #f5f5f5;
}

/* Toggle Container */
.toggle-container {
    display: flex;
    border: none;
    overflow: hidden;
    gap: 4px;
    align-items: center;
    flex: 1;
    width: 99%;
}

/* Toggle Buttons */
.toggle-btn {
        background-color: transparent;
        border: 1px solid #787680;
        border-radius: 5px;
        padding: 6px 15px;
        cursor: pointer;
        transition: background-color 0.2s ease-in-out;
        text-align: center;
        color: #64558F;
        font-size: 14px;
        font-family: Lato;
        font-weight: 500;
}

/* Active Toggle Button */
.toggle-btn.active {
    background: #E8DDFF;
    color: #64558F;
}

.toggle-btn#activity_overview:hover {
    background: #dbeafe;
}

.toggle-btn .checkmark {
    margin-right: 5px;
}

/* Border between toggle buttons */
.toggle-btn + .toggle-btn {
    border-left: 1px solid #d0d0d0;
}

/* Action Buttons Container */
.action-buttons {
    display: flex;
    align-items: center;
    width: 331px;
    height: 40px;
}

/* Action Buttons */
.action-btn {
    background: transparent;
    font-family: Lato, sans-serif;
    font-size: 12px;
    border: 0.5px solid #C8C5D0;
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 0px 16px;
    cursor: pointer;
    text-align: center;
    color: #001B3B;
    transition: background-color 0.2s, color 0.2s;
    flex: 1;
    height: 90%;
}

.action-btn.active {
  background-color: #dbeafe; /* Light blue for active */
  font-weight: 500;
}
.action-btn.left {
      /* Prevent double border in center */
     border-radius: 9999px 0 0 9999px; /* round only left side */
     border-right: 1px solid #C8C5D0; /* vertical separator */
}

.action-btn.right {
    border-left: none;
  border-radius: 0 9999px 9999px 0; /* round only right side */
}
.action-btn:hover {
    background-color: #dbeafe;
}

.expand-icon, .new-tab-icon {
    font-size: 14px;
}

/* Default mode adjustments */
.defaultMode .nav-container {
    display: flex;
}

/* Center Slider */
.slider-container {
    flex: 1;
    position: relative;
    height: 10px;
    background-color: #f0e6ff;
    border-radius: 5px;
    visibility: hidden;
}

.slider-track {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background-color: #f0e6ff;
    border-radius: 5px;
}

.slider-bar {
    position: absolute;
    top: 0;
    left: 30%;
    height: 100%;
    width: 40%;
    background-color: #6b4ea1;
    border-radius: 5px;
}

.commet-trail-container {
    display: flex;
    align-items: center;
    border: none;
    font-size: 10px;
    font-family: "Lato";
    overflow: hidden;
}

.item:first-child {
    flex: 1;
}

.commet-middle {
    display: flex;
    flex: 1;
    justify-content: space-between;
    align-items: center;
}

.commet-middle .commet-icon-container {
    display: flex;
    align-items: center;
    flex: 1;
    justify-content: center;
    padding: 0px 5px;
}

.commet-middle .commet-icon-container:last-child {
    border-right: 1px dashed #787680;
    border-left: 1px dashed #787680;
}

.commet-last-child {
    flex: 1;
    padding-left: 5px;
    text-align: right;
}

.commet-icon {
    width: 11px;
    height: 11px;
    border-radius: 50%;
    background-color: #FFFFFF;
    display: inline-block;
    margin: 0px 5px;
}

g.highcharts-axis-labels.highcharts-xaxis-labels.highcharts-grid-axis ::selection {
    background-color: transparent;
    color: #000;
}

/* Styles for material event shapes */
.material-event-container {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-left: -2px;
}

.material-shape {
    display: inline-block;
    width: 23px;
    height: 23px;
    margin-right: 8px;
    border: 1px solid #000;
}

.material-shape.circle {
    border-radius: 50%;
}

.material-shape.triangle {
    width: 0;
    height: 0;
    border-left: 12px solid transparent;
    border-right: 12px solid transparent;
    border-bottom: 23px solid;
    background-color: transparent;
    border-top: none;
    position: relative;
}

/* Add a pseudo-element for the black border around triangle */
.material-shape.triangle::after {
    content: '';
    position: absolute;
    top: 1px;
    left: -13px;
    width: 0;
    height: 0;
    border-left: 13px solid transparent;
    border-right: 13px solid transparent;
    border-bottom: 24px solid #000;
    z-index: -1;
}

.material-event-container .a_name {
    color: #000;
    font-weight: normal;
}
.grey-font{
    color: #787680;
}

/* Navigation container styles */
.toggle-container .chevron-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

.toggle-container .section-button {
  margin-right: 12px;
}

.toggle-container .vertical-divider {
  border-left: 1px solid;
  border-color: #C8C5D0;
  padding: 18px 7px;
}

.materialView {
    background-color: #EAFAF3;
}
button.active span.material-symbols-outlined.grey-font {
    color: #001B3B;
}

.toggle-container .chevron-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

.chevron-container {
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
    opacity: 0;
    transform: translateX(-10px);
}

.chevron-container:not(.hidden) {
    opacity: 1;
    transform: translateX(0);
}

/* View transition styles */
.innerChart {
    transition: opacity 0.3s ease-in-out;
    opacity: 0;
}

.innerChart:not(.hidden) {
    opacity: 1;
}

.defaultMode.subevent .nav-container #activity_overview{
    pointer-events: none;
    opacity: 0.5;
    cursor: not-allowed;
}
