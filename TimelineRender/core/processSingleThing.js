function(properties, context) {
    console.time("Function Execution");

    // Load framework-managed data
    var activity = properties.thing; // This may trigger "not ready" internally

    // Function to process activities with retries
    const processWithRetry = async () => {
        let retries = 0;
        const maxRetries = 10; // Adjust to avoid infinite loops
        let activityObject;
       


        while (retries < maxRetries) {
            try {
                // Attempt to unwrap activities
                activityObject = await deepUnwrap(activity);
                console.debug("Activities unwrapped:", activityObject);
                break; // Success, exit the retry loop
            } catch (err) {
                // Check if the error is a "not ready" error using hasOwnProperty
                if (err.hasOwnProperty('not_ready_key')) {
                    retries++;
                    console.debug(`Retry ${retries}/${maxRetries} due to NotReadyError:`, err);
                    await new Promise(resolve => setTimeout(resolve, 500)); // Wait 500ms before retrying
                } else {
                    // A different error occurred; log and re-throw
                    console.error("Unexpected error in unWrapActivities:", err);
                    throw err;
                }
            }
        }

        if (!activityObject) {
            //handletimelineError
            throw new Error("Max retries reached; data still not ready");
        }
        // Data is ready; process it
        //TODO call scheudler here to something
        
        
        // return timelines;
    };

    // Initial check to align with framework’s "not ready" behavior
    if (activity === undefined || activity === null) {
        console.debug("Activities not ready yet");
        throw new Error("not ready");
    }

    // Call the async function with retries and return its result
     processWithRetry().finally(() => {
        console.timeEnd("Function Execution");
    });
}

