<section class="timeline"> <!-- section.materialView -->
  
  <!-- SUPERSET EVENTS -->
  <div id="dashboard" class="innerChart"></div>
  <!-- END SUPERSET EVENTS -->
  <!-- SUBSET EVENTS Deliverables-->
  <div id="detailView" class="innerChart hidden"></div>
  <!--END  SUBSET EVENTS -->
  <!-- SUBSET EVENTS Material Live Events-->
   <div id="liveEventView" class="innerChart hidden"></div>
   <!--END  SUBSET EVENTS -->
  <!-- CONTROLS -->
  <div class="nav-container">
    <div class="toggle-container">
      <button class="toggle-btn" id="activity_overview" fdprocessedid="d12zpj">Overview</button>
      <div class="chevron-container hidden" id="active_activity_container">
        <span class="material-symbols-outlined grey-font">
          chevron_right
        </span>
        <button class="toggle-btn section-button" id="active_activity">Hello</button>
        <div class="vertical-divider"></div>
        <div class="action-buttons">
          <button class="action-btn left active"><span class="material-symbols-outlined grey-font">
              check
            </span>Deliverable</button>
          <button class="action-btn right">Live</button>
        </div>
      </div>
    </div>
    <div>
      <div class="action-buttons">
        <button class="action-btn left" fdprocessedid="7vqr7s"><span class="material-symbols-outlined grey-font">
            fullscreen
          </span>Full page</button>
        <button class="action-btn right" fdprocessedid="yx235q"><span class="material-symbols-outlined grey-font">
            logout
          </span>Open in new tab</button>
      </div>
    </div>
  </div>

  <!-- END CONTROLS -->
  <!-- LOADING STATE -->
  <div class="loading-overlay">
    <img
      src="//69906f258885a4f4ebf7e70d3efa75db.cdn.bubble.io/f1729596239489x668010031180732400/Infinite%20Loader%20%287%29.gif"
      alt="Loading...">
  </div>
  <!-- END LOADING STATE -->
  <!-- ERROR STATE -->
  <div class="error-overlay">
    <div></div>
    <div class="dialog">
      <h5 class="headline">There is a conflict in the setup of this <span id="timelinieType">activity</span>.</h5>
      <h6>Please resolve before you able to see any timeline update. </h6>
      <ul class="error_messages">
      </ul>
    </div>
    <div></div>
  </div>
  <!-- END ERROR STATE -->
</section>